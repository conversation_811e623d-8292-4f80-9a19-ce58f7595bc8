package gocron

import (
	"brand-bidding-service/domain/task/service"
	"brand-bidding-service/infra/constant"
	"fmt"
	"github.com/robfig/cron/v3"
)

func Init() {
	c := cron.New()
	// 定时任务：
	//添加定时任务：@every 48h / 35 * * * * / 0 1 * * *
	if _, err := c.AddFunc("1,31 * * * *", func() {
		go service.TaskSyncOrderToSheet(constant.OrderStatusWithAll)
	}); err != nil {
		fmt.Println("定时任务创建失败: TaskSyncOrderToSheet", err.Error())
	}

	if _, err := c.AddFunc("10 01 * * *", func() {
		go service.TaskSyncOrderToSheet(constant.OrderStatusWithFinal)
	}); err != nil {
		fmt.Println("定时任务创建失败: TaskSyncOrderToSheet", err.Error())
	}

	// 每天凌晨2点同步商家数据
	if _, err := c.AddFunc("10 02 * * *", func() {
		go service.TaskSyncMerchantToSheet()
	}); err != nil {
		fmt.Println("定时任务创建失败: TaskSyncMerchantToSheet", err.Error())
	}

	if _, err := c.AddFunc("10 03 * * *", func() {
		go service.TaskSyncMerchantBusinessMetricsToSheet()
	}); err != nil {
		fmt.Println("定时任务创建失败: TaskSyncMerchantBusinessMetricsToSheet", err.Error())
	}

	// 每天凌晨1点申请商家数据
	if _, err := c.AddFunc("15 01 * * *", func() {
		go service.TaskBatchApplyMerchant()
	}); err != nil {
		fmt.Println("定时任务创建失败: TaskBatchApplyMerchant", err.Error())
	}

	//if _, err := c.AddFunc("47 14 * * *", func() {
	//	go service.TaskSyncMerchantTrafficMetricsToSheet()
	//}); err != nil {
	//	fmt.Println("定时任务创建失败: TaskSyncMerchantToSheet", err.Error())
	//}

	c.Start()

	select {}
}
