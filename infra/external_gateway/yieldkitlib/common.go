package yieldkitlib

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const (
	host              = "https://api.yieldkit.com"
	hostConversions   = "https://account2.yieldkit.com"
	apiGetMerchants   = "/v1/advertiser"
	apiGetConversions = "/api/v3/reports/commissions/sales"
)

const (
	requestInterval = time.Millisecond * 300
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时300ms
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, fmt.Sprintf("%v", value)) // 自动转为字符串
	}
	fullUrl := baseUrl
	if strings.Contains(baseUrl, "?") {
		fullUrl = fmt.Sprintf("%s&%s", baseUrl, urlValues.Encode())
	} else {
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

var CategoryNameMap = map[string]string{
	"Adult":                     "Others",
	"Arts & Entertainment":      "Arts & Entertainment",
	"Automotive":                "Travel & Transportation",
	"Business":                  "Business & Industrial",
	"Careers":                   "Jobs & Education",
	"Education":                 "Jobs & Education",
	"Family & Parenting":        "People & Society",
	"Food & Drink":              "Food & Drink",
	"Health & Fitness":          "Health & Beauty",
	"Hobbies & Interests":       "Hobbies & Leisure",
	"Home & Garden":             "Home & Electronics",
	"News":                      "People & Society",
	"Personal Finance":          "Others",
	"Shopping":                  "Consumer Goods",
	"Society":                   "People & Society",
	"Sports":                    "Sports & Fitness",
	"Style & Fashion":           "Fashion & Apparel",
	"Technology & Computing":    "Home & Electronics",
	"Travel":                    "Travel & Transportation",
	"Uncategorized":             "Others",
	"Law, Gov't & Politics":     "People & Society",
	"Pets":                      "Pets & Animals",
	"Real Estate":               "Others",
	"Religion and Spirituality": "People & Society",
	"Science":                   "Science & Reference",
}
