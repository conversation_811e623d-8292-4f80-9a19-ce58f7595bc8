package yieldkitlib

import (
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/yieldkitlib/yieldkitvo"
	"brand-bidding-service/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
)

func GetMerchants(apiKey string, apiSecret string, siteId string, limit int, page int) (*yieldkitvo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"api_key":    apiKey,
		"api_secret": apiSecret,
		"site_id":    siteId,
		"page_size":  limit,
		"page":       page,
		"format":     "json",
	}

	headers := map[string]string{
		"accept":             "application/json, text/plain, */*",
		"accept-language":    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
		"cache-control":      "no-cache",
		"content-type":       "application/json",
		"pragma":             "no-cache",
		"priority":           "u=1, i",
		"sec-ch-ua":          "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": "\"macOS\"",
		"sec-fetch-dest":     "empty",
		"sec-fetch-mode":     "cors",
		"sec-fetch-site":     "same-site",
		"user-agent":         "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
	}
	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, ecode.ErrCodeHTTP.Err())
	}

	merchantResp := new(yieldkitvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		return nil, ecode.With(ecode.ErrCodeJson, ecode.ErrCodeJson.Err())
	}

	return merchantResp, nil
}

func BatchGetMerchants(apiKey string, apiSecret string, siteId string, limit int, page int, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	createDataRows := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	formattedDate := time.Now().Format("2006-01-02")
	
	for {
		resp, err := GetMerchants(apiKey, apiSecret, siteId, limit, page)
		if err != nil {
			continue
		}

		for _, row := range resp.Advertisers {
			uniqueKey := row.Id + row.Name
			if _, exists := uniqueMap[uniqueKey]; !exists {

				if len(row.Trackinglink) <= 0 {
					continue
				}
				category := "Other"
				if len(row.Categories) > 0 {
					category = row.Categories[0]
				}
				if _, ok := CategoryNameMap[category]; ok {
					category = CategoryNameMap[category]
				}
				merchantCommission := "0"
				merchantCpc := "0"
				merchantCr := "0"
				if len(row.Metrics) > 0 {
					merchantCommission = fmt.Sprintf("%v", row.Metrics[0].Commission)
					merchantCpc = fmt.Sprintf("%v", row.Metrics[0].Cpc)
					merchantCr = fmt.Sprintf("%v", row.Metrics[0].Cr)
				}
				if merchantCpc == "0" || merchantCpc == "<nil>" {
					continue
				}
				merchantRow := map[string]interface{}{
					"merchant_id":         row.Id,
					"merchant_slug":       row.Name,
					"merchant_name":       row.Name,
					"category_name":       category,
					"country":             strings.Join(row.Countries, ","),
					"supported_countries": strings.Join(row.Countries, ","),
					"website":             domainutil.ExtractDomain(row.Domain),
					"original_domain":     row.Domain,
					"cashback_info":       merchantCommission,
					"track_url":           row.Trackinglink,
					"network_partner":     "",
					"status":              1,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       "yieldkit",
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
					"offer_type":          row.OfferType,
					"pay_per_sale":        fmt.Sprintf("%v", row.PayPerSale),
					"pay_per_lead":        fmt.Sprintf("%v", row.PayPerLead),
					"cpc":                 merchantCpc,
					"cr":                  merchantCr,
					"commission":          merchantCommission,
				}
				createDataRows = append(createDataRows, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}

		// Check if we've reached the last page
		if page*limit >= resp.Total {
			break
		}
		fmt.Println(page, page*limit, resp.Total)
		page++
	}
	return createDataRows, nil
}
