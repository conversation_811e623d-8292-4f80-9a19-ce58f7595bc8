package yieldkitvo

import "time"

type GetMerchantsResp struct {
	Total       int `json:"total"`
	Size        int `json:"size"`
	Page        int `json:"page"`
	Advertisers []struct {
		Id           string   `json:"id"`
		Name         string   `json:"name"`
		Description  string   `json:"description"`
		Countries    []string `json:"countries"`
		Url          string   `json:"url"`
		Domain       string   `json:"domain"`
		Image        string   `json:"image"`
		PayPerLead   float64  `json:"payPerLead,omitempty"`
		PayPerSale   float64  `json:"payPerSale"`
		Currency     string   `json:"currency"`
		AutoRedirect bool     `json:"autoRedirect"`
		Trackinglink string   `json:"trackinglink"`
		Deeplink     bool     `json:"deeplink"`
		OfferType    string   `json:"offerType"`
		Categories   []string `json:"categories"`
		Metrics      []struct {
			Description string      `json:"description"`
			Country     string      `json:"country"`
			Currency    string      `json:"currency"`
			Cpc         interface{} `json:"cpc"`
			Cr          float64     `json:"cr"`
			Commission  float64     `json:"commission"`
		} `json:"metrics"`
	} `json:"advertisers"`
}

type GetOrderTransactionsResp struct {
	Self    string `json:"self"`
	Next    string `json:"next"`
	Content []struct {
		Id               string      `json:"id"`
		AdvertiserName   string      `json:"advertiserName"`
		Commission       float64     `json:"commission"`
		State            string      `json:"state"`
		Date             time.Time   `json:"date"`
		YkTag            string      `json:"ykTag"`
		AdvertiserId     string      `json:"advertiserId"`
		ModifiedDate     time.Time   `json:"modified_date"`
		OrderId          interface{} `json:"orderId"`
		Currency         string      `json:"currency"`
		Amount           float64     `json:"amount"`
		CommissionType   string      `json:"commissionType"`
		PayoutId         interface{} `json:"payoutId"`
		ClickCountryCode string      `json:"clickCountryCode"`
		SiteId           string      `json:"siteId"`
	} `json:"content"`
	PageSize int `json:"page_size"`
}
