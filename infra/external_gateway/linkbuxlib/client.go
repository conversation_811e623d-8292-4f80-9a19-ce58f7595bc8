package linkbuxlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/linkbuxlib/linkbuxvo"
	"brand-bidding-service/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*linkbuxvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-1-2")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-1-2")

	params := map[string]interface{}{
		"mod":        "medium",
		"op":         "transaction",
		"token":      string(token),
		"status":     string(status),
		"begin_date": string(beginDate),
		"end_date":   string(endDate),
		"page":       strconv.Itoa(page),
		"limit":      strconv.Itoa(limit),
	}

	headers := map[string]string{}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(linkbuxvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("linkbuxvo GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("linkbuxvo BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPage {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *linkbuxvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.List))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.List {
		tagCode := order.Uid
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		timestamp, err := strconv.ParseInt(order.OrderTime, 10, 64)
		if err != nil {
			fmt.Println("Error parsing timestamp:", err)
		}
		t := time.Unix(timestamp, 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.LinkbuxId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Mcid,
			"merchant_id":      order.Mid,
			"commission":       order.SaleComm,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Uid2,
			"ip":               "",
			"referer_url":      order.ClickRef,
			"customer_country": order.CustomerCountry,
			"currency":         "USD",
			"commission_usd":   order.SaleComm,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchants(token string, limit int, page int, relationship string) (*linkbuxvo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":          "medium",
		"op":           "monetization_api",
		"token":        token,
		"relationship": relationship,
		"limit":        strconv.Itoa(limit),
		"page":         strconv.Itoa(page),
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, nil, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	getMerchantsResp := new(linkbuxvo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsResp, nil
}

func BatchGetMerchants(token string, limit int, relationship string, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			zap.L().Error("linkbuxlib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.List {
			uniqueKey := merchant.Mid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         merchant.Mid,
					"merchant_slug":       merchant.Mcid,
					"merchant_name":       merchant.MerchantName,
					"category_name":       merchant.Categories,
					"country":             merchant.PrimaryRegion,
					"supported_countries": merchant.SupportRegion,
					"website":             domainutil.ExtractDomain(merchant.SiteUrl),
					"original_domain":     merchant.SiteUrl,
					"cashback_info":       merchant.CommDetail,
					"track_url":           merchant.TrackingUrl,
					"sub1":                "uid",
					"status":              merchant.Relationship, // 默认激活状态
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypeLinkBux,
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
				}
				allMerchants = append(allMerchants, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsResp.Data.TotalPage {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}
