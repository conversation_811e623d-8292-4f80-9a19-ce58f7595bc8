package blueafflib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/blueafflib/blueaffvo"
	"brand-bidding-service/infra/utils/domainutil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(apiKey string, status string, page int, limit int, startDay int, endDay int) (*blueaffvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{}
	headers := map[string]string{
		"API-KEY":      api<PERSON>ey,
		"Content-Type": "application/json",
	}
	requestBody := map[string]interface{}{
		"start_date":   beginDate,
		"end_date":     endDate,
		"current_page": page,
		"page_size":    limit,
	}
	// 将请求体编码为 JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetConversions, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))

	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(blueaffvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("blueafflib GetOrderTransactionsByPortal json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, apiKey string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)
	for {
		getOrderTransactionsByPortalResp, err := GetOrderTransactions(apiKey, status, page, limit, startDay, endDay)
		if err != nil {
			continue
		}
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsByPortalResp)...)
		if page >= getOrderTransactionsByPortalResp.Data.Paging.TotalPage {
			break
		}
		page += 1
	}
	allOrderMap := make(map[interface{}]bool, 0)
	for i := range allOrderList {
		allOrderMap[allOrderList[i]["id"]] = true
	}
	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *blueaffvo.GetOrderTransactionsResp) []map[string]interface{} {
	// publish 全部转换为了 美元，无需进行转换
	result := make([]map[string]interface{}, 0, len(orders.Data.Records))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.Records {
		tagCode := order.Sub1
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t, err := time.Parse("2006-01-02 15:04:05", order.ConvTime)
		if err != nil {
			fmt.Println("Error parsing time:", err)
		}
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		// Convert string to float64
		estRevenue, err := strconv.ParseFloat(order.EstRevenue, 64)
		if err != nil {
			fmt.Println("Error converting string to float:", err)
			continue
		}
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.ConversionId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.OfferName,
			"merchant_id":      order.OfferId,
			"commission":       estRevenue,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Sub2,
			"ip":               order.ConversionIp,
			"referer_url":      order.ClickId,
			"customer_country": order.Country,
			"currency":         order.Currency,
			"commission_usd":   estRevenue,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}

		result = append(result, orderMap)
	}

	return result
}

func GetMerchant(apiKey string, limit int, page int) (*blueaffvo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{}

	headers := map[string]string{
		"API-KEY":      apiKey,
		"Content-Type": "application/json",
	}

	requestBody := map[string]interface{}{
		"page_size":    limit,
		"current_page": page,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetMerchants, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	merchantResp := new(blueaffvo.GetMerchantsResp)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	return merchantResp, nil
}

func BatchGetMerchants(apiKey string, limit int, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		resp, err := GetMerchant(apiKey, limit, page)
		if err != nil {
			fmt.Println("GetMerchants:", err)
			continue
		}

		for _, row := range resp.Data.Records {
			uniqueKey := row.OfferId + row.OfferName
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         row.OfferId,
					"merchant_slug":       row.OfferName,
					"merchant_name":       row.OfferName,
					"category_name":       row.Category,
					"country":             row.PrimaryRegion,
					"supported_countries": strings.Join(row.SupportedRegion, ","),
					"website":             domainutil.ExtractDomain(row.Domain),
					"original_domain":     row.Domain,
					"cashback_info":       fmt.Sprintf("%s: %s-%s", row.CommissionType, row.CommissionValueMin, row.CommissionValueMax),
					"track_url":           row.TrackingLink,
					"sub1":                "sub1",
					"status":              row.Status,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypeBlueAff,
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
				}
				allMerchants = append(allMerchants, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}

		// Check if we've reached the last page
		if page >= resp.Data.Paging.TotalPage {
			break
		}
		page++
	}
	return allMerchants, nil
}
