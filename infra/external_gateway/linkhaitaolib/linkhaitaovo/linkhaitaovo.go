package linkhaitaovo

type GetOrderTransactionsResp struct {
	Offset   int `json:"offset"`
	PageSize int `json:"pageSize"`
	Status   struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data []struct {
		SignId      string      `json:"sign_id"`
		MId         string      `json:"m_id"`
		TsId        string      `json:"ts_id"`
		Sitename    string      `json:"sitename"`
		CrossDevice string      `json:"cross_device"`
		OrderId     string      `json:"order_id"`
		OrderTime   string      `json:"order_time"`
		ReportTime  string      `json:"report_time"`
		SaleAmount  string      `json:"sale_amount"`
		Cashback    string      `json:"cashback"`
		Status      string      `json:"status"`
		Tagcode     string      `json:"tagcode"`
		Rebate      interface{} `json:"rebate"`
		Maketool    string      `json:"maketool"`
		ProductID   string      `json:"productID"`
		BID         string      `json:"BID"`
		Num         string      `json:"num"`
		Tagcode2    string      `json:"tagcode2"`
		Ip          string      `json:"ip"`
		RefererUrl  string      `json:"referer_url"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Total string `json:"total"`
	List  []struct {
		Mcid                 string      `json:"mcid"`
		MId                  string      `json:"m_id"`
		MerchantName         string      `json:"merchant_name"`
		SiteUrl              string      `json:"site_url"`
		Logo                 string      `json:"logo"`
		OfferType            interface{} `json:"offer_type"`
		Country              string      `json:"country"`
		MonthlyVisits        string      `json:"monthly_visits"`
		MerchantStatus       string      `json:"merchant_status"`
		Datetime             string      `json:"datetime"`
		Relationship         string      `json:"relationship"`
		Rd                   string      `json:"rd"`
		SiteDesc             interface{} `json:"site_desc"`
		RestrictedKeywords   interface{} `json:"restricted_keywords"`
		SupportDeeplink      interface{} `json:"support_deeplink"`
		ApprovalMode         interface{} `json:"approval_mode"`
		SiteId               string      `json:"site_id"`
		SupportRegion        []string    `json:"support_region"`
		CommRate             interface{} `json:"comm_rate"`
		Categories           string      `json:"categories"`
		Currency             string      `json:"currency"`
		TrackingUrl          string      `json:"tracking_url"`
		TrafficPromotionType interface{} `json:"traffic_promotion_type"`
		PromotionArea        interface{} `json:"promotion_area"`
	} `json:"list"`
}

type GetMerchantsByHtmlResp struct {
	Mcid                string   `json:"mcid"`
	Mid                 string   `json:"mid"`
	BrandId             string   `json:"brand_id"`
	MerchantName        string   `json:"merchant_name"`
	CommRate            string   `json:"comm_rate"`
	CommDetail          string   `json:"comm_detail"`
	SiteUrl             string   `json:"site_url"`
	Logo                string   `json:"logo"`
	Categories          string   `json:"categories"`
	Tags                string   `json:"tags"`
	OfferType           string   `json:"offer_type"`
	NetworkPartner      string   `json:"network_partner"`
	AvgPaymentCycle     string   `json:"avg_payment_cycle"`
	AvgPayout           string   `json:"avg_payout"`
	Country             string   `json:"country"`
	SupportRegion       string   `json:"support_region"`
	MerchantStatus      string   `json:"merchant_status"`
	Datetime            string   `json:"datetime"`
	Relationship        string   `json:"relationship"`
	TrackingUrl         string   `json:"tracking_url"`
	TrackingUrlShort    string   `json:"tracking_url_short"`
	RD                  string   `json:"RD"`
	SiteDesc            string   `json:"site_desc"`
	FilterWords         []string `json:"filter_words"`
	CurrencyName        string   `json:"currency_name"`
	AllowSml            string   `json:"allow_sml"`
	PostAreaList        []string `json:"post_area_list"`
	RepName             string   `json:"rep_name"`
	RepEmail            string   `json:"rep_email"`
	MlinkHash           string   `json:"mlink_hash"`
	BrandType           string   `json:"brand_type"`
	SupportCouponordeal string   `json:"support_couponordeal"`
}

type GetMerchantsEpcByHtmlResp struct {
	Mcid         string `json:"mcid"`
	Mid          string `json:"mid"`
	MerchantName string `json:"merchant_name"`
	CommRate     string `json:"comm_rate"`
	Categories   string `json:"categories"`
	ApprovalType string `json:"approval_type"`

	AvgCommRate   string `json:"avg_comm_rate"`
	Country       string `json:"country"`
	ReturnDays    string `json:"return_days"`
	MonthlyVisits string `json:"monthly_visits"`
	Epc30Day      string `json:"epc_30_day"`
}
