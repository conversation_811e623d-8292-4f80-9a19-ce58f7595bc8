<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>111Skin UK CPS推广-111Skin UK CPS佣金-111Skin UK返利-LinkHaiTao</title>
  <meta name="description" content="LinkHaiTao每日为您更新111Skin UK CPS推广信息，搜罗111Skin UK佣金返利信息，为站长提供免费赚钱的CPS、CPA、CPL等网络广告，随时随地推广商品赚钱，佣金高、结算快，是站长网络赚钱优秀的广告联盟,让您网站流量轻松变现!">
  <meta name="keywords" content="111Skin UK CPS推广,111Skin UK CPS佣金,111Skin UK CPS">
  <link href="https://www.linkhaitao.com/index.php?mod=ad&op=mc_detail&mid=skinuk" rel="canonical"><link href="/static/style/base.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/header.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/showtips.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/home.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/nologin.css?version=20220805" type="text/css" rel="stylesheet" />
  <link href="/static/style/footer.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/reg.css" type="text/css" rel="stylesheet" />
  <link href="/static/style/common-header.css" type="text/css" rel="stylesheet" />
  <script src="/static/js/jquery.js" type="text/javascript" type="text/javascript"></script>
  <script src="/static/js/seajs/sea.js" type="text/javascript"></script>
  <script src="/static/js/seajs/sea-config.js" type="text/javascript"></script>
  <link rel="stylesheet" type="text/css" href="/static/style/postback_tracking.css?v=1743316253">
  <link href="/static/style/mc_detail.css?v=1743316253" rel="stylesheet" type="text/css">
  <link rel="stylesheet" href="/static/vendor/noty/noty.css?v=1743316253" />
  <script src="static/js/jquery-1.11.3.js" type="text/javascript"></script>

</head>
<body><script>
  $(function () {
    var langBtn = $('.linkhaitao-header-logo .linkhaitao-header-langchange'),
            langBox = langBtn.find('.lang-btn');
    langBtn.bind('click', function () {
      langBtn.toggleClass('open')
    })
    langA = langBtn.find('.lang-btn p a');
    langA.bind('click', function () {
      console.log('进入点击事件','aaaaaaa')
      var lang = $(this).attr('id-lang')
      var url = ''
      if (window.location.search) {
        if (window.location.search.indexOf('lang=') !== -1) {
          console.log(lang,'2222')
          url = window.location.pathname + setLang('lang', lang) + window.location
                  .hash
        } else {
          url = window.location.pathname + window.location.search + '&lang=' + lang +
                  window.location.hash
          console.log(lang,'333')
        }
      } else {
        url = window.location.pathname + '?lang=' + lang + window.location.hash
        console.log(lang,'444')
      }
      console.log(url,'11111')
      window.location.href = url
    })

    function setLang(variable, value) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      var str = '?'
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          pair[1] = value;
          if (i == 0) {
            str = str + pair[0] + '=' + value
          } else {
            str = str + '&' + pair[0] + '=' + value
          }

        } else {
          if (i == 0) {
            str = str + vars[i]
          } else {
            str = str + '&' + vars[i]
          }

        }
      }
      return str;
    }

    $('.linkhaitao-nav-contaner li.disabled').bind('click', function() {
      alert($('#locked_warning').html());
    })

  })
</script>
<div class="linkhaitao-header">
  <div class="linkhaitao-header-content" > <div
          class="linkhaitao-header-logo">
    <a class="homepage" href="/index.php"></a>
    <div class="linkhaitao-header-langchange">
      中文
      <em class="arrow"></em>
      <div class="lang-btn">
        <p>
          <a href="javascript:void(0)" id-lang="cn">中文</a>
        </p>
        <p>
          <a href="javascript:void(0)" id-lang="en">English</a>
        </p>
        <p>
          <a href="javascript:void(0)" id-lang="kr">한국어</a>
        </p>
        <p>
          <a href="javascript:void(0)" id-lang="jp">日本語</a>
        </p>
        <p>
          <a href="javascript:void(0)" id-lang="ru">Русский</a>
        </p>
        <p>
          <a href="javascript:void(0)" id-lang="fr">Français</a>
        </p>
      </div>
    </div>
    <div style="display: none" id="locked_warning">
      您的账号已经被移除，请停止推广Linkhaitao平台的广告主</div>
    <div class="linkhaitao-header-info">
      <div class="linkhaitao-header-user">
        <p class="linkhaitao-header-user-hy fl header-old">
          <span>切换至新版本</span>
          <img src="/static/images/new-icon.svg" alt="">
        </p>
        <p class="linkhaitao-header-user-hy fl">
          欢迎 &nbsp;
          <span class="linkhaitao-header-username">baize</span>
        </p>
        <p class="linkhaitao-header-logout fl">
          <img src="/static/images/logout.png" class="fl">
          <a href="/index.php?mod=login&amp;op=logout" class="fr">
            退出</a>
        </p>
      </div>
    </div>
  </div>
  </div>
  <div class="linkhaitao-header-nav cn_nav">
    <div class="linkhaitao-nav-contaner" > <ul class="ui-text-capitalize">
      <li > <a href="/index.php">首页</a></li>
      <li > <a href="/index.php?aff=1">佣金总览</a></li>
      <li  class="active" > <a href="/index.php?mod=ad&amp;op=index">广告活动</a></li>

      <li > <a href="/index.php?mod=analysis&amp;op=new_index">统计报表</a></li>
      <li > <a href="/index.php?mod=site">站点管理</a></li>
      <li > <a href="/index.php?mod=maketool&amp;op=coupon_list">投放工具</a></li>
      <li > <a href="/index.php?mod=maketool&amp;op=wx_products_list">小程序商品</a></li>

      <li > <a href="/index.php?mod=finance">佣金申请</a></li>
      <li > <a href="/index.php?mod=user">个人中心</a></li>
      <li > <a href="/brand.html">品牌中心</a></li>
    </ul>
    </div>
  </div>
</div>
<script src="https://www.linkhaitao.com/gdpr-modal.min.js?version=2025033014" type="text/javascript" type="text/javascript"></script>
<script>
  var lang = "cn";
  if (location.href.indexOf('linkhaitao.com') > -1) {
    new GDPR({
      checkedGDPR: false,
      acceptText: 'Accept',
      cookieDomain: '.linkhaitao.com',
      policyLink: 'https://www.linkhaitao.com/help/0.html?lang=' + lang,
      thirdScript: function () {
        (function ttt(t, n) {
          var e, r = document.createElement("script"),
                  a = document.getElementsByTagName("script")[0],
                  o = {
                    cb: n
                  },
                  i = 20;
          e = function () {
            window.___RMCMPW && "function" == typeof window.___RMCMPW ? window.___RMCMPW(
                    o) : i < 1 ? o.cb({
              status: 3,
              isGdpr: function () {
                var t = ["DE", "UK", "GB", "FR", "IT", "ES", "PL", "NL",
                          "RO", "BE", "CZ", "SE", "HU",
                          "EL", "GR", "PT", "AT", "OE", "DK", "FI", "SK",
                          "IE", "BG", "HR", "LT", "LV", "SI",
                          "EE", "CY", "LU", "MT", "150", "039", "151", "154",
                          "155"
                        ],
                        n = [].concat(navigator.languages || []);
                n.push(navigator.userLanguage || navigator.language || "");
                for (var e = null, r = 0; r < n.length; r++)
                  for (var a = n[r].split("-"), o = a.length > 6 ? 6 : a
                          .length, i = 1; i < o; i++)
                    if (e = !0, t.indexOf(a[i].toUpperCase()) > 1)
                      return !0;
                return null == e && null
              }()
            }) : (i = 1, window.setTimeout(e, 100))
          }, r.setAttribute("src", t), a.parentNode.insertBefore(r, a), e()
        }(
                "//intljs.rmtag.com/119900.ct.js",
                function (co) {
// console.log("Consent Object => " + JSON.stringify(co));
                }
        ));
      }
    })
  }
</script>

<style>
  /**
  * tips style
  */
  .lh-tips {
    width: 1180px;
    margin: 10px auto;
    position: relative;
    padding: 4px 10px;
    background-color: #f1f7ff;
    border: 1px solid #5c6280;
    color: #737475;
  }

  .lh-tips::before {
    width: 16px;
    height: 8px;
    position: absolute;
    top: -8px;
    left: 10px;
    content: '';
    background: url('/static/images/arrow-top.svg');
    background-size: 100%;
  }

  .lh-tips img {
    margin-left: 1px;
    vertical-align: middle;
    margin-top: -3px;
    margin-right: 5px;
  }

  .lh-tips a {
    text-decoration: underline;
    color: #343e60;
    padding: 0 2px;
  }

  .lh-tips span {
    cursor: pointer;
    float: right;
    margin-right: 10px;
  }
  .header-old {
    cursor: pointer;
  }
  .header-old span {
    color: #585F82;
    text-align: center;
    font-family: Nunito;
    font-size: 14px;
    font-style: normal;
  }
  .header-old img {
    vertical-align: -9px;
  }
</style>

<script>
  // tips相关
  $(function () {

    $('.header-old').click(function () {
      location.href = '/dashboard';
    })

    function setCookie(name, value) {
      var Days = 30;
      var exp = new Date();
      exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
      document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
    }

//读取cookies
    function getCookie(name) {
      var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

      if (arr = document.cookie.match(reg))

        return unescape(arr[2]);
      else
        return null;
    }

//删除cookies
    function delCookie(name) {
      var exp = new Date();
      exp.setTime(exp.getTime() - 1);
      var cval = getCookie(name);
      if (cval != null)
        document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString();
    }

// 点击close移除对应的tips并且写入cookie记忆
    $('.lh-tips span').click(function () {
      var self = $(this),
              parEle = self.parents('.lh-tips'),
              id = parEle.data('id');

      setCookie(id, 'opened');

      parEle.remove();
    })

// 页面加载后判断有cookie的移除不显示
    $('.lh-tips').each(function () {
      var self = $(this),
              id = self.data('id'),
              has_clicked = getCookie(id);

      if (has_clicked) {
        self.remove();
      }
    })

// 退出时清空tips弹窗显示cookies
    $('.linkhaitao-header-logout').click(function (e) {
      delCookie('tips-site');
      delCookie('tips-withdraw');
      delCookie('tips-idcard');
      delCookie('tips-tax');
      delCookie('tips-tixian');
      delCookie('tips-withdraw-time');
    })
  })
</script>
<div class="ad-detal-content">
  <h2 class="ui-text-capitalize">广告主详情</h2>
  <div class="ad-detail-main"><link href="/static/style/linkbox.css" type="text/css" rel="stylesheet" />
    <div class="ad-detail-webChoose">
      <label class="ui-text-capitalize">选择投放网站</label>
      <form action="" id="join-form">
        <select name="site_id" id="site_id" data-url="index.php?mod=ad&amp;op=mc_detail&amp;mid=skinuk">
          <option value="11306" >Premium Brand Reviews **********</option>
        </select>
        <input type="hidden" id="mid" name="m_id" value="skinuk"/>
      </form>
    </div>
    <div class="ad-datail-webDetail clearfix mt15">
      <div class="clearfix fl mc-info">
        <div class="logo-div"><img src="//cdn.linkhaitao.com/data/mc_logo/111skinuk.png?v=30"></div>
        <div>
          <dl>
            <dd>ID: 16478</dd>
            <dd>111Skin UK</dd>
            <dd>网址：<a href="https://111skin.co.uk/" rel="nofollow" target="_blank"><strong>https://111skin.co.uk/</strong></a></dd>
            <dd>简介：<strong></strong></dd>
          </dl>
        </div>
      </div>
      <div class="webDetail-right-btn fr clearfix">
        <a href="index.php?mod=notice&amp;type=3&amp;m_id=skinuk">广告主公告</a>

        <button>未加入</button>

        <a href="index.php?mod=ad&amp;op=editlinkpage&amp;mid=skinuk&amp;site_id=11306"  class="editlink">自定义链接</a>
        <button class="joined" id="join">审核中</button>

      </div>
    </div>
    <script>
      //var url = "/index.php?mod=ad&op=mc_detail&mid=skinuk";
      seajs.use(['jquery'],function($){
        $(document).ready(function(){
          $('#site_id').change(function(event) {
            var url              = $(this).data('url');
            var id               = $(this).val();
            var name             = $(this).attr('name');
            var href             = url + '&' + name + '=' + + id;
            window.location.href = href;
          });
          $('#join').click(function(event) {
            var _mid = $("#mid").val();
            if($(this).hasClass('joined'))
              return;
            $.ajax({
              type : "POST",
              url : '/index.php?mod=ad&op=adv_apply',
              datatype : 'json',
              data : {m_id:_mid,site_id:$('#site_id').val()},//提交数据
              success : function(data) {
                data = JSON.parse(data);
                if (data.status.error_code == 0){
                  //申请成功
                  alert("申请成功");
                  window.location.reload();
                }
                else{
                  //返回失败信息
                  alert(data.status.error_message);
                }
              }
            });
          });
        });
      })
    </script>
    <div style="border:1px solid #eae9e9;" class="mt10">
      <table width="1198" border="1" cellspacing="0" cellpadding="0" class="table1">
        <tr>
          <td class="title" width="160">数据返回机制</td>
          <td colspan="4">3小时~5小时</td>
        </tr>
        <tr>
          <td class="title">最新结算期</td>
          <td colspan="4">160天</td>
        </tr>
        <tr>
          <td class="title">广告效果认定期间</td>
          <td colspan="4">认可广告被点击后30.00日内产生的业绩</td>
        </tr>
        <tr>
          <td class="title" rowspan="3">控制条件</td>
          <td class="title" width="140">u_id数</td>
          <td width="270">200</td>
          <td class="title" width="140">自定义链接</td>
          <td width="278">支持</td>
        </tr>
        <tr>
          <td class="title">购物返现</td>
          <td>支持</td>
          <td class="title">类似域名</td>
          <td>支持</td>
        </tr>
        <tr>
          <td class="title">禁止关键词</td>
          <td style="width: 33%;">
            <div class="forbidden-key">
              <input type="text" value="" id="input-val">

              <p></p>
            </div>
          </td>
          <td class="title">移动端</td>
          <td>支持</td>
        </tr>
        <tr>
          <td class="title" rowspan="4">广告链接类型</td>
          <td class="title">一般链接</td>
          <td colspan="3">
            <a href="index.php?mod=ad&amp;op=general_link&amp;mid=skinuk&amp;site_id=11306">获取一般链接</a>
            (广告主更新时，需要重新粘贴链接)
          </td>
        </tr>
        <tr>
          <td class="title">自定义链接</td>
          <td colspan="3">
            <a href="index.php?mod=ad&amp;op=editlinkpage&amp;mid=skinuk&amp;site_id=11306">获取自定义链接 （请您亲手输入图片URL、文字内容、目标URL，可以生成您所要的链接）</a>
            （请您亲手输入图片URL、文字内容、目标URL，可以生成您所要的链接)
          </td>
        </tr>
        <tr>
          <td class="title">详细说明</td>
          <td colspan="4">
            <br />
          </td>
        </tr>

      </table>
    </div>

    <div class="region region_action1">
      <h2 class="active"><b></b> 邮寄国家</h2>
      <div class="country-wrap" style="display: flex;"><div class="country-item">GB</div>
      </div>
    </div>


    <!-- 当前佣金支付标准/历史佣金支付标准-->
    <div class="reward-standard mt40">
      <h3>
        <span class="active">当前佣金支付标准</span>
        <span>历史佣金支付标准</span>
      </h3>
      <div class="mt20 standard-mode" style="border:1px solid #eae9e9;">
        <table width="1198" border="1" cellspacing="0" cellpadding="0" class="table2">
          <tr>
            <td class="title" width="230">名称</td>
            <td class="title" width="72">佣金&nbsp;<div class="question">?
              <div class="text">
                <p>当佣金标准显示为“Rev.Share: 70%”时，意味着LinkHaitao将向您支付我们从商家收到的销售佣金的70%。</p>
              </div>
            </div>
            </td>
            <td class="title" width="156">佣金周期</td>
            <td class="title" width="82">适用商品</td>
            <td class="title" width="448">详情说明</td>
          </tr><tr>
          <td class="alr">
            收入占比（比例参考“详情说明”）
          </td>
          <td>
            4.9%</td>
          <td>2025-03-30 00:00:00~2026-02-28 23:59:59</td>
          <td>&nbsp;</td>
          <td class="alr">
            <span class="ad-btns" data-modal="504190">View</span>
            <div class="modal" id="detailIds" modal-cont="504190" style="display: none;">
              <div class="modal-bg"></div>
              <div class="modal-cont" style="width: 450px;">
                <div class="modal-hd">
                  <div class="modal-title">Details</div>
                  <span class="modal-close"></span>
                </div>
                <div class="modal-copy">
                  <div class="mod-btn">
                    <div class="btn copy-detail" data-clipboard-target="#copy_code_504190">Copy Details</div>
                  </div>
                  <table class="ad-table" id="copy_code_504190" style="width: 100%; margin: 15px 0 16px;">
                    <tbody>
                    <tr>
                      <th class="ad-table__color-gray" width="220" style="text-align:center;">Commission Name</th>
                      <th class="ad-table__color-gray" style="text-align:center;">Rate<span class="icon-wen" data-width="420px"
                                                                                            data-content="Your Commission Rate = Commission% * Rate（ex：Commission=Rev.Share: 70%，Rate=10%，Your Commission Rate=70%*10%=7%）。">?</span></th>
                    </tr><tr>
                      <td class="ad-table__color-black">Default</td>
                      <td class="ad-table__color-black">4.9%</td>
                    </tr>


                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </td>
        </tr>
        </table>
      </div>
      <div class="mt20 standard-mode" style="border:1px solid #eae9e9; display:none">
        <table width="1198" border="1" cellspacing="0" cellpadding="0" class="table2">
          <tr>
            <td class="title" width="230">名称</td>
            <td class="title" width="72">佣金&nbsp;<div class="question">?
              <div class="text">
                <p>当佣金标准显示为“Rev.Share: 70%”时，意味着LinkHaitao将向您支付我们从商家收到的销售佣金的70%。</p>
              </div>
            </div>
            </td>
            <td class="title" width="156">佣金周期</td>
            <td class="title" width="82">适用商品</td>
            <td class="title" width="448">详情说明</td>
          </tr><tr>
          <td class="alr">
            全站商品
          </td>
          <td>
            5.00 %</td>
          <td>2022-01-18 00:00:00~2025-01-23 23:59:59</td>
          <td>&nbsp;</td>
          <td class="alr"></td>
          <!-- <td class="alr">
          <span class="ad-btns" data-modal="39809">View</span>
          <div class="modal" id="detailIds" modal-cont="39809" style="display: none;">
          <div class="modal-bg"></div>
          <div class="modal-cont" style="width: 500px;">
          <div class="modal-hd">
          <div class="modal-title">Details</div>
          <span class="modal-close"></span>
          </div>
          <div class="modal-copy">
          <div class="mod-btn">
          <div class="btn copy-detail" data-clipboard-target="#copy_code_39809">Copy Details</div>
          </div>
          <table class="ad-table" id="copy_code_39809" style="width: 100%; margin: 15px 0 16px;">
          <tbody>
          <tr>
          <th class="ad-table__color-gray" width="220">Commission Name</th>
          <th class="ad-table__color-gray">Rate<span class="icon-wen" data-width="420px"
          data-content="Your commission rates = the commission items*XX%">?</span></th>
          </tr>
          <tr>
          <td class="ad-table__color-black">Default</td>
          <td class="ad-table__color-black">12%</td>
          </tr>
          <tr>
          <td class="ad-table__color-black">Health</td>
          <td class="ad-table__color-black">16%</td>
          </tr>
          </tbody>
          </table>
          </div>
          </div>
          </div>
          </td> -->
        </tr>
          <tr>
            <td class="alr">
              Base Commission
            </td>
            <td>
              1.40 %</td>
            <td>2025-01-24 00:00:00~2025-02-11 16:35:03</td>
            <td>&nbsp;</td>
            <td class="alr">Default:2.00%</td>
            <!-- <td class="alr">
            <span class="ad-btns" data-modal="503483">View</span>
            <div class="modal" id="detailIds" modal-cont="503483" style="display: none;">
            <div class="modal-bg"></div>
            <div class="modal-cont" style="width: 500px;">
            <div class="modal-hd">
            <div class="modal-title">Details</div>
            <span class="modal-close"></span>
            </div>
            <div class="modal-copy">
            <div class="mod-btn">
            <div class="btn copy-detail" data-clipboard-target="#copy_code_503483">Copy Details</div>
            </div>
            <table class="ad-table" id="copy_code_503483" style="width: 100%; margin: 15px 0 16px;">
            <tbody>
            <tr>
            <th class="ad-table__color-gray" width="220">Commission Name</th>
            <th class="ad-table__color-gray">Rate<span class="icon-wen" data-width="420px"
            data-content="Your commission rates = the commission items*XX%">?</span></th>
            </tr>
            <tr>
            <td class="ad-table__color-black">Default</td>
            <td class="ad-table__color-black">12%</td>
            </tr>
            <tr>
            <td class="ad-table__color-black">Health</td>
            <td class="ad-table__color-black">16%</td>
            </tr>
            </tbody>
            </table>
            </div>
            </div>
            </div>
            </td> -->
          </tr>
          <tr>
            <td class="alr">
              Default Commission
            </td>
            <td>
              4.50 %</td>
            <td>2025-02-11 16:35:12~2025-03-29 23:59:59</td>
            <td>&nbsp;</td>
            <td class="alr"></td>
            <!-- <td class="alr">
            <span class="ad-btns" data-modal="504189">View</span>
            <div class="modal" id="detailIds" modal-cont="504189" style="display: none;">
            <div class="modal-bg"></div>
            <div class="modal-cont" style="width: 500px;">
            <div class="modal-hd">
            <div class="modal-title">Details</div>
            <span class="modal-close"></span>
            </div>
            <div class="modal-copy">
            <div class="mod-btn">
            <div class="btn copy-detail" data-clipboard-target="#copy_code_504189">Copy Details</div>
            </div>
            <table class="ad-table" id="copy_code_504189" style="width: 100%; margin: 15px 0 16px;">
            <tbody>
            <tr>
            <th class="ad-table__color-gray" width="220">Commission Name</th>
            <th class="ad-table__color-gray">Rate<span class="icon-wen" data-width="420px"
            data-content="Your commission rates = the commission items*XX%">?</span></th>
            </tr>
            <tr>
            <td class="ad-table__color-black">Default</td>
            <td class="ad-table__color-black">12%</td>
            </tr>
            <tr>
            <td class="ad-table__color-black">Health</td>
            <td class="ad-table__color-black">16%</td>
            </tr>
            </tbody>
            </table>
            </div>
            </div>
            </div>
            </td> -->
          </tr>
        </table>
      </div>
      <h4 class="current-ad"><b></b>最近6个月发布的广告公告</h4>
      <div style="border:1px solid #eae9e9;">
        <table width="1198" border="1" cellspacing="0" cellpadding="0" class="table2">
          <tr>
            <td class="title" width="120">时间</td>
            <td class="title" width="142">公告类型</td>
            <td class="title" width="730">标题</td>
          </tr></table>
      </div>
    </div>
    <!-- /当前佣金支付标准/历史佣金支付标准-->

  </div>
</div>
<div class="modal" id="keys-dialog">
  <div class="modal-cont">
    <div class="lh-close">
      <img src="/static/images/icon-close.png" alt="">
    </div>
    <div class="lh-title">
      <span>禁止关键词</span>
      <span class="j-copy" data-clipboard-target="#input-val">复制</span>
    </div>
    <div class="lh-keys" id="lh-keys"></div>
  </div>
</div><div class="linkhaitao-footer">
  <div class="linkhaitao-footer-container">
    <div class="linkhaitao-footer-copyright">
      <div class="linkhaitao-footer-topline"></div>
      <p>
        <a href="/">首页</a> |
        <a href="/aboutus">关于我们</a> |
        <a href="/aboutus?s=contactus">联系我们</a> |
        <a href="/privacy-policy">隐私协议</a> |
        <a href="/help">帮助</a>
      </p>
      <p>沪公网安备：沪B2-20190332 | <img src="/static/pic/jh.png"/><a href="https://beian.miit.gov.cn/" target="_blank">沪ICP备19030265号-1</a></p>
      <p>©2014-2025 linkhaitao.com All Rights Reserved | 违法和不良信息举报电话：021-61910511转8012</p>
    </div>
  </div>
</div>
</body>
</html><script>
  var _hmt = _hmt || [];
  (function() {
    var hm = document.createElement("script");
    hm.src = "//hm.baidu.com/hm.js?53760f061b903b8e61845f4fdaaae91d";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
  })();
</script>

<!-- Global site tag (gtag.js) - Google Analytics -->
<!-- Modified By Ricardo (Tongtong) Liu -->
<!-- <script src="https://www.googletagmanager.com/gtag/js?id=UA-XXXXX-Y" type="text/javascript"></script> -->
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'UA-206883571-1');
  window.addEventListener('load', function(){
    var s = document.createElement('script');
    s.src = "https://www.googletagmanager.com/gtag/js?id=UA-206883571-1";
    document.body.appendChild(s);
  });
</script>

<script>
  $(document).ready(function() {
    function jsonload() {
      window.tracking = new TrackingPlugin({
        apiProxy: '/xs.gif',
        source: 'LinkHaitao',
        app: '前台',
        appVersion: '1.0.0',
        uid: '90757',
      });
    }

    // 创建 <script> 标签并设置 onload 事件处理程序
    var scriptElement = document.createElement('script');
    scriptElement.async = true;
    scriptElement.onload = jsonload;
    scriptElement.src = '/tracker.min.js?id=' + new Date().getTime();;
    scriptElement.type = 'text/javascript';

    // 将 <script> 标签添加到页面
    document.head.appendChild(scriptElement);
  });
</script>
<script src="/static/vendor/clipboard/clipboard.min.js" type="text/javascript"></script>
<script src="/static/vendor/noty/noty.min.js" type="text/javascript"></script>
<script>

  seajs.use(['jquery'],function($){

    $('.ad-btns').on('click', function() {
      var modal = $(this).data('modal');
      $('[modal-cont="' + modal + '"]').show();
    })

    var clipboard = new Clipboard('.copy-detail');
    clipboard.on('success', function(e) {
      e.clearSelection();
      new Noty({
        type: 'success',
        progressBar: false,
        timeout: 2000,
        text: '拷贝成功',
      }).show();
    });

    $('.modal-bg, .modal-close').on('click', function() {
      $('.modal').hide();
    })

    function copyText(el, callback) {
      var input = document.querySelector(el);
      input.select();
      document.execCommand("copy"); // 执行浏览器复制命令
      callback();
    }

    var text = $('.forbidden-key p').text().replace(/\s/g, ''),
            textLen = text.length;

    if (
            textLen > 150
    ) {
      $('.forbidden-key p').text(text.substr(0, 130) + '...');
      $('.forbidden-key .btns').show();
    }

    $('.j-copy').click(function() {
      if ($('#input-val').val()) {
        copyText('#input-val', function() {
          new Noty({
            type: 'success',
            progressBar: false,
            timeout: 2000,
            text: '拷贝成功',
          }).show();
        })
      } else {
        new Noty({
          type: 'success',
          progressBar: false,
          timeout: 2000,
          text: '复制失败，请手动复制',
        }).show();
      }
    })

    $('.lh-close').click(function() {
      $('#keys-dialog').hide();
    })

    $('.forbidden-key .btns').click(function() {
      $('#lh-keys').html($('#input-val').val());
      $('#keys-dialog').show();
    })


//切换
    $('.reward-standard h3 span').bind('click',function(){
      $(this).addClass('active').siblings().removeClass('active');
      $('.standard-mode').eq($(this).index()).css('display','block');
      $('.standard-mode').eq($(this).index()).siblings('.standard-mode').css('display','none');
    })
//弹出提示框
    showmsg = function showmsg(msg,time){
      $('.linkhaitao-popdlg').removeClass('none');
      $('.linkhaitao-popdlg-textbox p').text(msg);
      if(time){
        setTimeout(function(){
          $('.linkhaitao-popdlg').addClass('none');
        },time);
      }
    }
    var ajax_lock = false;
    $('button#join').click(function(event) {
      if(ajax_lock)
        return;
      ajax_lock = true;
      $.ajax({
        url: 'index.php?mod=ad&op=adv_apply',
        type: "POST",
        async: false,
        data: $('#join-form').serialize(),
        success:function(data){
          _data = JSON.parse(data);
          if (_data.status.error_code) {
            alert(_data.status.error_message);
            return;
          }
          alert('申请成功');
          ajax_lock = false;
          window.location.reload();
        },
        error:function(){
          ajax_lock = false;
        }
      });
    });

// 展开折叠 region   ，，，，，，，。。。。
    $('.region_action1 h2').bind('click',function(){
      console.log(1111,$(this).attr('class'))
      if($(this).attr('class') == "active"){
        $(this).removeClass('active')
        $('.region_action1 .country-wrap').css('display','none')
      }else{
        $(this).addClass('active')
        $('.region_action1 .country-wrap').css('display','flex')
      }
    })

    $('.region_action2 h2').bind('click',function(){
      console.log(1111,$(this).attr('class'))
      if($(this).attr('class') == "active"){
        $(this).removeClass('active')
        $('.region_action2 .country-wrap').css('display','none')
      }else{
        $(this).addClass('active')
        $('.region_action2 .country-wrap').css('display','flex')
      }
    })
  })
</script>
