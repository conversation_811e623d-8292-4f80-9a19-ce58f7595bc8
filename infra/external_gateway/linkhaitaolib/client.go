package linkhaitaolib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/linkhaitaolib/linkhaitaovo"
	"brand-bidding-service/infra/utils/domainutil"
	"brand-bidding-service/infra/utils/safeutil"
	"context"
	"encoding/json"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, offset int, limit int, startDay int, endDay int) (*linkhaitaovo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{
		"mod":        "medium",
		"op":         "cashback",
		"token":      string(token),
		"status":     string(status),
		"begin_date": string(beginDate),
		"end_date":   string(endDate),
		"offset":     strconv.Itoa(offset),
		"pageSize":   strconv.Itoa(limit),
	}

	headers := map[string]string{}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(linkhaitaovo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("linkhaitaolib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, offset int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, offset, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("linkhaitaolib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 如果获取到的订单长度为0，表示没有更多订单
		if len(getOrderTransactionsResp.Data) == 0 {
			break
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 更新 offset，以便获取下一批订单
		offset += limit
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *linkhaitaovo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data {
		tagCode := order.Tagcode
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		timestamp, err := strconv.ParseInt(order.OrderTime, 10, 64)
		if err != nil {
			fmt.Println("Error parsing timestamp:", err)
		}
		t := time.Unix(timestamp, 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.SignId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.MId,
			"merchant_id":      order.MId,
			"commission":       order.Cashback,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Tagcode2,
			"ip":               order.Ip,
			"referer_url":      order.RefererUrl,
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   order.Cashback,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

// ApplyMerchants applies to multiple merchants at once using the multi_apply endpoint
func ApplyMerchants(cookie string, merchantIds []string, siteId int) *ecode.ErrCode {
	ctx := context.Background()
	chunks := make([][]string, 0)
	for i := 0; i < len(merchantIds); i += 20 {
		end := i + 20
		if end > len(merchantIds) {
			end = len(merchantIds)
		}
		chunks = append(chunks, merchantIds[i:end])
	}
	headers := map[string]string{
		"Cookie":           cookie,
		"Content-Type":     "application/x-www-form-urlencoded; charset=UTF-8",
		"Referer":          "https://www.linkhaitao.com/index.php?mod=ad&op=index",
		"origin":           "https://www.linkhaitao.com",
		"X-Requested-With": "XMLHttpRequest",
	}
	for _, chunk := range chunks {
		formData := url.Values{}
		formData.Set("site_id", strconv.Itoa(siteId))
		for _, merchantId := range chunk {
			formData.Add("m_id[]", merchantId)
		}
		// Make the request
		_, err := remoteInvokeWithUrl(ctx, host+apiMultiApply, http.MethodPost, nil, headers, strings.NewReader(formData.Encode()))
		if err != nil {
			return ecode.With(ecode.ErrCodeHTTP, err)
		}
	}
	return nil
}

// GetMerchants retrieves a list of merchants
func GetMerchants(token string, limit int, page int, relationship string) (*linkhaitaovo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":      "medium",
		"op":       "merchantBasicList3",
		"token":    token,
		"per_page": strconv.Itoa(limit),
		"page":     strconv.Itoa(page),
	}
	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, nil, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getMerchantsResp := new(linkhaitaovo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("linkhaitaolib GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}

	return getMerchantsResp, nil
}

// BatchGetMerchants retrieves all merchants with pagination
func BatchGetMerchants(token string, limit int, accountName string, relationship string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// Call GetMerchants function
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			zap.L().Error("linkhaitaolib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// Process merchant data
		for _, merchant := range getMerchantsResp.List {
			uniqueKey := merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         merchant.MId,
					"merchant_slug":       merchant.Mcid,
					"merchant_name":       merchant.MerchantName,
					"category_name":       merchant.Categories,
					"country":             merchant.Country,
					"supported_countries": strings.Join(merchant.SupportRegion, ","),
					"website":             domainutil.ExtractDomain(merchant.SiteUrl),
					"original_domain":     merchant.SiteUrl,
					"cashback_info":       fmt.Sprintf("%v", merchant.CommRate),
					"track_url":           merchant.TrackingUrl,
					"sub1":                "tag",
					"network_partner":     "",
					"status":              merchant.Relationship,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypeLinkHaiTao,
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
				}
				allMerchants = append(allMerchants, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}

		// Update offset and check if we need to continue
		total, errc := strconv.Atoi(getMerchantsResp.Total)
		if errc != nil {
			zap.L().Error("linkhaitaolib strconv.Atoi failed", zap.Error(err))
			return allMerchants, err
		}
		if page*limit >= total {
			break
		}
		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}

func GetMerchantsByHtml(cookie string, siteId int, page int, relationship string) ([]*linkhaitaovo.GetMerchantsByHtmlResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":         "ad",
		"op":          "index",
		"join_status": relationship,
		"site_id":     strconv.Itoa(siteId),
		"page":        strconv.Itoa(page),
	}

	headers := map[string]string{
		"Cookie": cookie,
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiAdminGetMerchants, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(resp)))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getMerchantsByHtml := make([]*linkhaitaovo.GetMerchantsByHtmlResp, 0)
	doc.Find("div.main-table table tbody tr").Each(func(i int, s *goquery.Selection) {
		merchant := &linkhaitaovo.GetMerchantsByHtmlResp{}
		if mId, exists := s.Find("td strong.apply_status").Attr("data-id"); exists {
			merchant.Mid = strings.TrimSpace(mId)
		}
		getMerchantsByHtml = append(getMerchantsByHtml, merchant)
	})
	fmt.Println(getMerchantsByHtml)
	return getMerchantsByHtml, nil
}

// BatchGetMerchantsByHtml retrieves all merchants with pagination
func BatchGetMerchantsByHtml(cookie string, siteId int, accountName string, relationship string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	page := 1
	maxPage := 150
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// Call GetMerchants function
		getMerchantsResp, err := GetMerchantsByHtml(cookie, siteId, page, relationship)
		if err != nil {
			fmt.Println("linkhaitaolib BatchGetMerchantsByHtml GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// Process merchant data
		for _, merchant := range getMerchantsResp {
			merchantMap := map[string]interface{}{
				"merchant_id":         merchant.Mid,
				"merchant_slug":       merchant.Mid, // 使用Mid作为slug
				"merchant_name":       "",           // HTML版本缺少名称信息
				"category_name":       "",
				"country":             "",
				"supported_countries": "",
				"website":             "",
				"original_domain":     "",
				"cashback_info":       "",
				"track_url":           "",
				"network_partner":     "",
				"status":              1, // 默认激活状态
				"user_name":           accountName,
				"account":             accountName,
				"platform_type":       "linkhaitao",
				"created_at":          formattedDate,
				"updated_at":          formattedDate,
				// 平台特定字段
				"relationship": relationship,
			}
			allMerchants = append(allMerchants, merchantMap)
		}

		if len(getMerchantsResp) < 20 || page >= maxPage {
			break
		}
		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}

func GetAndApplyNewMerchants(cookie string, siteId int, accountName string) *ecode.ErrCode {
	// 获取所有没有关系的商家
	merchants, err := BatchGetMerchantsByHtml(cookie, siteId, accountName, "none")
	if err != nil {
		return err
	}

	// 找出未申请的商家ID
	var unappliedMerchantIds []string
	for _, merchant := range merchants {
		if merchantId := safeutil.SafeString(merchant["merchant_id"]); merchantId != "" {
			unappliedMerchantIds = append(unappliedMerchantIds, merchantId)
		}
	}

	// 如果有未申请的商家，进行申请
	if len(unappliedMerchantIds) > 0 {
		if err := ApplyMerchants(cookie, unappliedMerchantIds, siteId); err != nil {
			return err
		}
	}
	return nil
}

func GetMerchantsEpcByHtml(cookie string, siteId int, limit int, page int, relationship string) ([]*linkhaitaovo.GetMerchantsEpcByHtmlResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"join_status": relationship,
		"channel":     strconv.Itoa(siteId),
		"page":        strconv.Itoa(page),
		"page_size":   strconv.Itoa(limit),
	}

	headers := map[string]string{
		"Cookie": cookie,
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiAdminGetMyAdvertisers, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(resp)))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getMerchantsEpcByHtml := make([]*linkhaitaovo.GetMerchantsEpcByHtmlResp, 0)
	doc.Find("div.programs-list div.merchant-box div.merchant-box__item").Each(func(i int, s *goquery.Selection) {
		merchant := &linkhaitaovo.GetMerchantsEpcByHtmlResp{}
		merchant.Mcid = strings.ReplaceAll(strings.TrimSpace(s.Find("div.merchant-box__left div.merchant-info h5 span.id").Text()), "|  ID: ", "")
		merchant.Mid = strings.TrimSpace(s.Find("div.merchant-box__left div.merchant-check label span.el-checkbox__label").Text())
		merchant.MerchantName = strings.TrimSpace(s.Find("div.merchant-box__left div.merchant-info h5 span.name").Text())
		merchant.Categories = strings.TrimSpace(s.Find("div.merchant-box__left div.merchant-info p span.el-tooltip").Eq(0).Text())
		merchant.Country = strings.TrimSpace(s.Find("div.merchant-box__left div.merchant-info p span.el-tooltip").Eq(1).Text())
		merchant.MonthlyVisits = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item p").Eq(0).Text())
		merchant.ReturnDays = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item p").Eq(1).Text())
		merchant.Epc30Day = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item span").Eq(0).Text())
		merchant.CommRate = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item p").Eq(2).Text())
		merchant.ApprovalType = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item p").Eq(3).Text())
		merchant.AvgCommRate = strings.TrimSpace(s.Find("div.merchant-box__right div.merchant-info div.merchant-info__item span").Eq(1).Text())
		getMerchantsEpcByHtml = append(getMerchantsEpcByHtml, merchant)
	})
	return getMerchantsEpcByHtml, nil
}

func BatchGetMerchantsEpcByHtml(cookie string, siteId int, limit int, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchantsEpc := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsEpcResp, err := GetMerchantsEpcByHtml(cookie, siteId, limit, page, "adopt")
		if err != nil {
			fmt.Println("linkhaitaolib BatchGetMerchantsEpc GetMerchantsEpc failed", zap.Error(err))
			return allMerchantsEpc, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsEpcResp {

			day30EpcFloat, err1 := strconv.ParseFloat(merchant.Epc30Day, 64)
			avgCommRateFloat, err2 := strconv.ParseFloat(merchant.AvgCommRate, 64)
			// 如果任一转换失败，则直接返回"否"
			if err1 != nil || err2 != nil {
				continue
			}

			if day30EpcFloat < 5 || avgCommRateFloat <= 0 {
				continue
			}

			// 获取商家的最新结算期
			settlementDate, _ := GetMerchantsSettlementDateByHtml(cookie, merchant.Mid)
			uniqueKey := merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         merchant.Mid,
					"merchant_slug":       merchant.Mcid,
					"merchant_name":       merchant.MerchantName,
					"category_name":       merchant.Categories,
					"country":             merchant.Country,
					"supported_countries": merchant.Country,
					"website":             "",
					"original_domain":     "",
					"cashback_info":       merchant.CommRate,
					"track_url":           "",
					"network_partner":     "",
					"status":              1, // 默认激活状态
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       "linkhaitao",
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
					// 平台特定字段
					"epc_30_day":      merchant.Epc30Day,
					"avg_comm_rate":   merchant.AvgCommRate,
					"monthly_visits":  merchant.MonthlyVisits,
					"settlement_date": settlementDate,
					"approval_type":   merchant.ApprovalType,
					"return_days":     merchant.ReturnDays,
				}
				allMerchantsEpc = append(allMerchantsEpc, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}
		// 如果当前页是最后一页，退出循环
		if len(getMerchantsEpcResp) < limit {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchantsEpc, nil
}

func GetMerchantsSettlementDateByHtml(cookie string, mid string) (string, *ecode.ErrCode) {
	settlementDate := "-1"
	ctx := context.Background()

	params := map[string]interface{}{
		"mod":  "ad",
		"mid":  mid,
		"op":   "mc_detail",
		"lang": "cn",
	}

	headers := map[string]string{
		"Cookie": cookie,
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiAdminGetMerchants, http.MethodGet, params, headers, nil)
	if err != nil {
		return settlementDate, ecode.With(ecode.ErrCodeHTTP, err)
	}
	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(resp)))
	if err != nil {
		return settlementDate, ecode.With(ecode.ErrCodeHTTP, err)
	}
	settlementDate = strings.TrimSuffix(doc.Find("td.title:contains('最新结算期')").Next().Text(), "天")
	fmt.Println(mid, settlementDate)
	return settlementDate, nil
}
