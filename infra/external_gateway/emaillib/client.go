package emaillib

import (
	"brand-bidding-service/infra/external_gateway/emaillib/emailvo"
	"fmt"
	"net/smtp"
)

func SendEmail(config *emailvo.EmailConfig, to []string, subject string, body string) error {
	// 设置邮件头和邮件内容
	message := fmt.Sprintf("From: %s\nTo: %s\nSubject: %s\n\n%s",
		config.From, to, subject, body)

	// 使用 PlainAuth 进行 SMTP 身份认证
	auth := smtp.PlainAuth("", config.From, config.Password, config.Host)

	// 拼接SMTP地址
	smtpAddr := fmt.Sprintf("%s:%s", config.Host, config.Port)

	// 发送邮件
	err := smtp.SendMail(smtpAddr, auth, config.From, to, []byte(message))
	if err != nil {
		return fmt.Errorf("发送邮件失败: %v", err)
	}
	return nil
}
