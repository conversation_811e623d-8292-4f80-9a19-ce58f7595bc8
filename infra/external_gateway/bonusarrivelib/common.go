package bonusarrivelib

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
)

const (
	host = "https://www.bonusarrive.com"

	apiGetOrderTransactions = "/slapi/service/transactions"
)

const (
	requestInterval = time.Second * 3
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时300ms
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}
