package bonusarrivevo

type GetOrderTransactionsResp struct {
	Status int    `json:"status"`
	Info   string `json:"info"`
	Data   struct {
		TotalItems int `json:"total_items"`
		TotalPage  int `json:"total_page"`
		List       []struct {
			SignId      string `json:"sign_id"`
			MId         string `json:"m_id"`
			OrderTime   string `json:"order_time"`
			ReportTime  string `json:"report_time"`
			OrderAmount string `json:"order_amount"`
			Currency    string `json:"currency"`
			Commission  string `json:"commission"`
			Status      string `json:"status"`
			SubId       string `json:"sub_id"`
			SubId2      string `json:"sub_id2"`
			Num         string `json:"num"`
		} `json:"list"`
	} `json:"data"`
}
