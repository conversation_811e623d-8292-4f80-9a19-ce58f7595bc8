package bonusarrivelib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/bonusarrivelib/bonusarrivevo"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*bonusarrivevo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-1-2")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-1-2")

	params := map[string]interface{}{}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json;charset=utf-8",
	}

	requestBody := map[string]interface{}{
		"begin_date": beginDate,
		"end_date":   endDate,
		"page":       page,
		"per_page":   limit,
	}
	// 将请求体编码为 JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(bonusarrivevo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("bonusarrivelib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("bonusarrivelib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPage {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *bonusarrivevo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.List))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.List {
		tagCode := order.SubId
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t, err := time.Parse("2006-01-02 15:04:05", order.OrderTime)
		if err != nil {
			fmt.Println("Error parsing time:", err)
		}
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.SignId,
			"account":          accountName,
			"order_id":         order.SignId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.MId,
			"merchant_id":      order.MId,
			"commission":       order.Commission,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.SubId2,
			"ip":               "",
			"referer_url":      "",
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   order.Commission,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}
