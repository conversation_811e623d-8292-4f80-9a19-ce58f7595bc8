package sheetlib

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var gclient *http.Client

func initClient(ctx context.Context) (*http.Client, error) {
	b, err := ioutil.ReadFile("./config/google_sheet_auth.json")
	if err != nil {
		return nil, err
	}
	// If modifying these scopes, delete your previously saved token.json.
	config, err := google.ConfigFromJSON(b, "https://www.googleapis.com/auth/spreadsheets")
	if err != nil {
		return nil, err
	}
	tok, err := tokenFromFile("./config/token.json")
	if err != nil {
		return nil, err
	}
	gclient = config.Client(ctx, tok)

	return gclient, nil
}

// Retrieve a token, saves the token, then returns the generated client.
func getClient(config *oauth2.Config) *http.Client {
	// The file token.json stores the user's access and refresh tokens, and is
	// created automatically when the authorization flow completes for the first
	// time.
	tokFile := "token.json"
	tok, err := tokenFromFile(tokFile)
	if err != nil {
		//tok = getTokenFromWeb(config)
		//saveToken(tokFile, tok)
		log.Fatalf("Unable to read token: %v", err)
	}
	return config.Client(context.Background(), tok)
}

// Request a token from the web, then returns the retrieved token.
func getTokenFromWeb(config *oauth2.Config) *oauth2.Token {
	authURL := config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	fmt.Printf("Go to the following link in your browser then type the "+
		"authorization code: \n%v\n", authURL)

	var authCode string
	if _, err := fmt.Scan(&authCode); err != nil {
		log.Fatalf("Unable to read authorization code: %v", err)
	}

	tok, err := config.Exchange(context.TODO(), authCode)
	if err != nil {
		log.Fatalf("Unable to retrieve token from web: %v", err)
	}
	return tok
}

// Retrieves a token from a local file.
func tokenFromFile(file string) (*oauth2.Token, error) {
	f, err := os.Open(file)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	tok := &oauth2.Token{}
	err = json.NewDecoder(f).Decode(tok)
	return tok, err
}

// Saves a token to a file path.
func saveToken(path string, token *oauth2.Token) {
	fmt.Printf("Saving credential file to: %s\n", path)
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		log.Fatalf("Unable to cache oauth token: %v", err)
	}
	defer f.Close()
	json.NewEncoder(f).Encode(token)
}

func GetHeaders(data interface{}) []interface{} {
	var headers []interface{}
	v := reflect.ValueOf(data).Elem() // the struct variable
	for i := 0; i < v.NumField(); i++ {
		fieldInfo := v.Type().Field(i) // a reflect.StructField
		tag := fieldInfo.Tag           // a reflect.StructTag
		name := tag.Get("json")
		if name == "" {
			name = strings.ToLower(fieldInfo.Name)
		}

		headers = append(headers, name)
	}

	return headers
}

func GetValues(data interface{}) []interface{} {
	var values []interface{}
	v := reflect.ValueOf(data).Elem() // the struct variable
	for i := 0; i < v.NumField(); i++ {
		values = append(values, v.Field(i).Interface())
	}

	return values
}

func InitService(ctx context.Context, credentialsJson string) (*sheets.Service, error) {
	srv, err := sheets.NewService(ctx, option.WithCredentialsJSON([]byte(credentialsJson)))
	if err != nil {
		return nil, err
	}
	return srv, nil
}

func Update(ctx context.Context, spreadsheetId string, credentialsJson string, tabName string, tabRange string, rows [][]interface{}) error {
	if len(rows) == 0 {
		return nil
	}
	spreadsheetId = getGoogleSheetIdByGoogleSheetUrl(spreadsheetId)
	tabRange = tabName + "!" + tabRange
	srv, err := sheets.NewService(ctx, option.WithCredentialsJSON([]byte(credentialsJson)))
	if err != nil {
		return err
	}

	_, err = srv.Spreadsheets.Values.BatchUpdate(spreadsheetId, &sheets.BatchUpdateValuesRequest{ValueInputOption: "USER_ENTERED", Data: []*sheets.ValueRange{{
		Range:  tabRange,
		Values: rows,
	}}}).Do()
	if err != nil {
		return err
	}

	return nil
}

func Append(ctx context.Context, spreadsheetId string, credentialsJson string, tabName string, tabRange string, rows [][]interface{}) error {
	if len(rows) == 0 {
		return nil
	}
	spreadsheetId = getGoogleSheetIdByGoogleSheetUrl(spreadsheetId)
	tabRange = tabName + "!" + tabRange
	srv, err := sheets.NewService(ctx, option.WithCredentialsJSON([]byte(credentialsJson)))
	if err != nil {
		return err
	}

	_, err = srv.Spreadsheets.Values.Append(spreadsheetId, tabRange, &sheets.ValueRange{
		Values: rows,
	}).ValueInputOption("USER_ENTERED").Do()
	//}).ValueInputOption("USER_ENTERED").InsertDataOption("OVERWRITE").Do()
	if err != nil {
		return err
	}

	return nil
}

func Get(ctx context.Context, spreadsheetId string, credentialsJson string, tabName string, tabRange string) ([][]interface{}, error) {
	spreadsheetId = getGoogleSheetIdByGoogleSheetUrl(spreadsheetId)

	tabRange = tabName + "!" + tabRange
	srv, err := sheets.NewService(ctx, option.WithCredentialsJSON([]byte(credentialsJson)))
	if err != nil {
		return nil, err
	}
	resp, err := srv.Spreadsheets.Values.Get(spreadsheetId, tabRange).Do()
	if err != nil {
		return nil, err
	}
	return resp.Values, nil
}

// getGoogleSheetIdByGoogleSheetUrl 根据 google sheet url 获取 google sheet id
func getGoogleSheetIdByGoogleSheetUrl(googleSheetUrl string) string {
	googleSheetId := googleSheetUrl
	x := strings.Split(googleSheetUrl, "/")
	for idx := range x {
		if x[idx] == "spreadsheets" {
			googleSheetId = x[idx+2]
			break
		}
	}

	return googleSheetId
}

// InsertDataToLastRow 将数据追加到工作表的最后一行
func InsertDataToLastRow(ctx context.Context, spreadsheetId string, credentialsJson string, tabName string, tabRange string, data [][]string) error {
	// 将 [][]string 转换为 [][]interface{}
	rows := make([][]interface{}, len(data))
	for i, row := range data {
		rows[i] = make([]interface{}, len(row))
		for j, cell := range row {
			rows[i][j] = cell
		}
	}
	// 使用 Append 方法添加数据
	return Append(ctx, spreadsheetId, credentialsJson, tabName, tabRange, rows)
}

// parseRange 解析表格范围，支持多种格式：
// - "A:S" 格式
// - "A2:S" 格式
// - "A2:S100" 格式
// 返回起始列、结束列、起始行（如果指定）
func parseRange(tabRange string) (startCol, endCol string, startRow int, err error) {
	startRow = 1 // 默认从第1行开始

	// 移除所有空格
	tabRange = strings.ReplaceAll(tabRange, " ", "")

	parts := strings.Split(tabRange, ":")
	if len(parts) != 2 {
		return "", "", 0, fmt.Errorf("invalid range format: %s, expected format like 'A:S' or 'A2:S'", tabRange)
	}

	// 解析起始部分
	startPart := parts[0]
	startColMatch := regexp.MustCompile(`^([A-Z]+)(\d*)$`).FindStringSubmatch(startPart)
	if startColMatch == nil {
		return "", "", 0, fmt.Errorf("invalid start range format: %s", startPart)
	}
	startCol = startColMatch[1]
	if startColMatch[2] != "" {
		row, err := strconv.Atoi(startColMatch[2])
		if err != nil {
			return "", "", 0, fmt.Errorf("invalid start row: %s", startColMatch[2])
		}
		startRow = row
	}

	// 解析结束部分
	endPart := parts[1]
	endColMatch := regexp.MustCompile(`^([A-Z]+)(\d*)$`).FindStringSubmatch(endPart)
	if endColMatch == nil {
		return "", "", 0, fmt.Errorf("invalid end range format: %s", endPart)
	}
	endCol = endColMatch[1]

	return startCol, endCol, startRow, nil
}

// GetAllInBatches 自动探测并分批读取所有数据，避免超时
// batchSize 指定每批读取的行数
// tabRange 支持以下格式：
// - "A:S" 从A列到S列，从第1行开始
// - "A2:S" 从A列到S列，从第2行开始
// - "A2:S100" 从A列到S列，从第2行开始（结束行会被忽略，因为我们会自动探测到末尾）
func GetAllInBatches(ctx context.Context, spreadsheetId string, credentialsJson string, tabName string, tabRange string, batchSize int) ([][]interface{}, error) {
	spreadsheetId = getGoogleSheetIdByGoogleSheetUrl(spreadsheetId)

	// 解析范围
	startCol, endCol, startRow, err := parseRange(tabRange)
	if err != nil {
		return nil, fmt.Errorf("failed to parse range: %v", err)
	}

	srv, err := sheets.NewService(ctx, option.WithCredentialsJSON([]byte(credentialsJson)))
	if err != nil {
		return nil, fmt.Errorf("failed to create sheets service: %v", err)
	}

	var allValues [][]interface{}
	currentRow := startRow

	for {
		// 构造范围字符串，例如 "Sheet1!A1:S1000"
		batchRange := fmt.Sprintf("%s!%s%d:%s%d", tabName, startCol, currentRow, endCol, currentRow+batchSize-1)

		resp, err := srv.Spreadsheets.Values.Get(spreadsheetId, batchRange).Do()
		if err != nil {
			return nil, fmt.Errorf("failed to get values for range %s: %v", batchRange, err)
		}

		// 如果没有更多数据了，就退出循环
		if len(resp.Values) == 0 {
			break
		}

		allValues = append(allValues, resp.Values...)

		// 如果返回的数据行数小于 batchSize，说明已经读取到最后一批数据
		if len(resp.Values) < batchSize {
			break
		}

		currentRow += batchSize
		time.Sleep(1 * time.Second)
	}

	return allValues, nil
}
