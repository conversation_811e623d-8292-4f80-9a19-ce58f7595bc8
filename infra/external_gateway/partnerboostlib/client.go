package partnerboostlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/partnerboostlib/partnerboostvo"
	"brand-bidding-service/infra/utils/domainutil"
	"brand-bidding-service/infra/utils/safeutil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*partnerboostvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{
		"token":      token,
		"begin_date": beginDate,
		"end_date":   endDate,
		"page":       page,
		"limit":      limit,
		"mod":        "medium",
		"op":         "transaction",
	}

	headers := map[string]string{}

	requestBody := map[string]interface{}{}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(partnerboostvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPage {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *partnerboostvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.List))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.List {
		tagCode := order.Uid
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 转换为 time.Time
		orderTimeInt, _ := strconv.Atoi(order.OrderTime)
		t := time.Unix(int64(orderTimeInt), 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.PartnerboostId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Mcid,
			"merchant_id":      order.BrandId,
			"commission":       order.SaleComm,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Uid2,
			"ip":               "",
			"referer_url":      order.ClickRef,
			"customer_country": order.CustomerCountry,
			"currency":         "USD",
			"commission_usd":   order.SaleComm,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchants(token string, limit int, page int, relationship string) (*partnerboostvo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{}
	requestBody := map[string]interface{}{
		"source":  "partnermatic",
		"token":   token,
		"curPage": strconv.Itoa(page),
		"perPage": strconv.Itoa(limit),
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodPost, params, nil, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	getMerchantsResp := new(partnerboostvo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("partnerboostvo GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsResp, nil
}

func BatchGetMerchants(token string, limit int, relationship string, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			fmt.Println("partnerboostlib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.List {
			uniqueKey := merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantRow := map[string]interface{}{
					"merchant_id":         strconv.Itoa(merchant.Mid),
					"merchant_slug":       merchant.Mcid,
					"merchant_name":       merchant.MerchantName,
					"category_name":       merchant.Categories,
					"country":             merchant.Country,
					"supported_countries": merchant.SupportRegion,
					"website":             domainutil.ExtractDomain(merchant.SiteUrl),
					"original_domain":     merchant.SiteUrl,
					"cashback_info":       merchant.CommRate,
					"track_url":           merchant.TrackingUrl,
					"network_partner":     merchant.NetworkPartner,
					"status":              1,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       "pb",
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
					"relationship":        merchant.Relationship,
					"rd":                  merchant.RD,
					"avg_payment_cycle":   strconv.Itoa(merchant.AvgPaymentCycle),
					"avg_payout":          merchant.AvgPayout,
					"offer_type":          merchant.OfferType,
				}
				allMerchants = append(allMerchants, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsResp.Data.TotalPage {
			break
		}
		fmt.Println("pb: ", page, getMerchantsResp.Data.TotalPage)
		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}

func GetAndApplyNewMerchants(token string, authorization string, cookie string, limit int, accountName string, siteIds string) *ecode.ErrCode {
	// 获取所有没有关系的商家
	merchants, err := BatchGetMerchants(token, limit, "No Relationship", accountName, "")
	if err != nil {
		return err
	}

	// 找出未申请的商家ID
	var unappliedMerchantIds []string
	for _, merchant := range merchants {
		if merchantId := safeutil.SafeString(merchant["merchant_slug"]); merchantId != "" {
			unappliedMerchantIds = append(unappliedMerchantIds, merchantId)
		}
	}

	// 如果有未申请的商家，进行申请
	if len(unappliedMerchantIds) > 0 {
		if err := ApplyMerchants(authorization, cookie, unappliedMerchantIds, siteIds); err != nil {
			return err
		}
	}
	return nil
}

func ApplyMerchants(authorization string, cookie string, merchantIds []string, siteIds string) *ecode.ErrCode {
	ctx := context.Background()

	// 将商家ID列表按50个一组分割
	chunks := make([][]string, 0)
	for i := 0; i < len(merchantIds); i += 50 {
		end := i + 50
		if end > len(merchantIds) {
			end = len(merchantIds)
		}
		chunks = append(chunks, merchantIds[i:end])
	}

	headers := map[string]string{
		"Authorization": authorization,
		"Cookie":        cookie,
		"Referer":       "https://app.partnerboost.com/partner/brands/news",
		"Accept":        "application/json",
		"Running-Mode":  "partner",
	}

	// 对每一组商家ID进行申请
	for _, chunk := range chunks {
		bodyData := map[string]interface{}{
			"content":  "",
			"m_ids":    strings.Join(chunk, ","),
			"site_ids": siteIds,
		}
		bodyDataByte, err := json.Marshal(bodyData)
		if err != nil {
			return ecode.With(ecode.ErrCodeJson, err)
		}
		_, err = remoteInvokeWithUrl(ctx, host+apiAdminApplyMerchant, http.MethodPost, nil, headers, bytes.NewReader(bodyDataByte))
		if err != nil {
			return ecode.With(ecode.ErrCodeHTTP, err)
		}

		// 根据API文档要求，每次申请后等待4秒
		time.Sleep(4 * time.Second)
	}

	return nil
}

func GetMerchantsEpc(authorization string, cookie string, limit int, page int) (*partnerboostvo.GetMerchantsEpcResp, *ecode.ErrCode) {
	//ctx := context.Background()
	//params := map[string]interface{}{
	//	"sort_by":   "desc",
	//	"list_type": "my_brand",
	//	"page_size": strconv.Itoa(limit),
	//	"page_num":  strconv.Itoa(page),
	//	"n":         42555,
	//	"t":         int(time.Now().Unix()),
	//	"h":         "00007e2131e3ca10f2b49a8d207a4f92",
	//	"d":         4,
	//}
	//headers := map[string]string{
	//	"Authorization": authorization,
	//	"Cookie":        cookie,
	//	"Running-Mode":  "partner",
	//}
	//resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchantsEpc, http.MethodGet, params, headers, nil)
	//if err != nil {
	//	fmt.Println("err:", err)
	//	return nil, ecode.With(ecode.ErrCodeHTTP, err)
	//}
	resp := ``
	getMerchantsEpcResp := new(partnerboostvo.GetMerchantsEpcResp)
	err := json.Unmarshal([]byte(resp), getMerchantsEpcResp)
	if err != nil {
		fmt.Println("partnerboostlib GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsEpcResp, nil
}

func BatchGetMerchantsEpc(authorization string, cookie string, limit int, accountName string) ([][]string, *ecode.ErrCode) {
	allMerchantsEpc := make([][]string, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsEpcResp, err := GetMerchantsEpc(authorization, cookie, limit, page)
		if err != nil {
			zap.L().Error("partnerboostlib BatchGetMerchantsEpc GetMerchantsEpc failed", zap.Error(err))
			return allMerchantsEpc, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsEpcResp.Data.Data {
			day30Epc := ""
			day30Epc = strings.ReplaceAll(merchant.Day30Epc, "$", "")
			day30Epc = strings.ReplaceAll(day30Epc, ",", "")
			day30EpcFloat, errc := strconv.ParseFloat(day30Epc, 64)
			if errc != nil {
				// 处理错误
				fmt.Println("转换错误:", errc)
				continue
			}
			if day30EpcFloat < 0 {
				return allMerchantsEpc, nil
			}
			uniqueKey := merchant.Id
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantRow := []string{
					merchant.Id,
					merchant.MId,
					merchant.Sitename,
					merchant.Category,
					merchant.Country,
					strings.Join(merchant.SupportRegions, ","),
					domainutil.ExtractDomain(merchant.SiteUrl),
					formattedDate,
					accountName,
					day30Epc,
					merchant.BillingCycleUnit,
					merchant.CR,
					merchant.Acceptance,
					fmt.Sprintf("%v", merchant.IsAmazon),
				}
				allMerchantsEpc = append(allMerchantsEpc, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsEpcResp.Data.TotalPage {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchantsEpc, nil
}
