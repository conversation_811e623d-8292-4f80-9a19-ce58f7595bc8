package partnerboostvo

type GetMerchantsEpcResp struct {
	Data struct {
		TotalSize   int `json:"total_size"`
		TotalPage   int `json:"total_page"`
		PageSize    int `json:"page_size"`
		PageNum     int `json:"page_num"`
		CurrentSize int `json:"current_size"`
		Data        []struct {
			Id               string   `json:"id"`
			Logo             string   `json:"logo"`
			SiteUrl          string   `json:"site_url"`
			Sitename         string   `json:"sitename"`
			MId              string   `json:"m_id"`
			CId              string   `json:"c_id"`
			Addr             string   `json:"addr"`
			Userid           string   `json:"userid"`
			UnionId          string   `json:"union_id"`
			BillingCycleUnit string   `json:"billing_cycle_unit"`
			PrimaryRegion    string   `json:"primary_region"`
			SpecialType      string   `json:"special_type"`
			PricingModel     string   `json:"pricing_model"`
			PerSale          string   `json:"per_sale"`
			DefaultSale      string   `json:"default_sale"`
			Day30Epc         string   `json:"day_30_epc"`
			Acceptance       string   `json:"acceptance"`
			CR               string   `json:"CR"`
			Country          string   `json:"country"`
			Category         string   `json:"category"`
			IsCampaign       int      `json:"is_campaign"`
			IsCouponTracking int      `json:"is_coupon_tracking"`
			IsFollow         int      `json:"is_follow"`
			SupportRegions   []string `json:"support_regions"`
			ProductNumbers   int      `json:"product_numbers"`
			SiteList         []struct {
				PropertyType string `json:"property_type"`
				CName        string `json:"c_name"`
				Id           string `json:"id"`
				MediumId     string `json:"medium_id"`
				Title        string `json:"title"`
				Host         string `json:"host"`
				ApplyStatus  string `json:"apply_status"`
				UpdatedTime  string `json:"updated_time"`
				Isdefault    string `json:"isdefault"`
				AdvId        string `json:"adv_id"`
			} `json:"site_list"`
			SiteApprovedList []struct {
				PropertyType string `json:"property_type"`
				CName        string `json:"c_name"`
				Id           string `json:"id"`
				MediumId     string `json:"medium_id"`
				Title        string `json:"title"`
				Host         string `json:"host"`
				ApplyStatus  string `json:"apply_status"`
				UpdatedTime  string `json:"updated_time"`
				Isdefault    string `json:"isdefault"`
				AdvId        string `json:"adv_id"`
			} `json:"site_approved_list"`
			SitePendingList        []interface{} `json:"site_pending_list"`
			SiteDeclinedList       []interface{} `json:"site_declined_list"`
			SiteNoRelationshipList []interface{} `json:"site_no_relationship_list"`
			SiteSuspendedList      []interface{} `json:"site_suspended_list"`
			IsApply                int           `json:"is_apply"`
			TrackingCode           struct {
				Current []interface{} `json:"current"`
				Expired []interface{} `json:"expired"`
			} `json:"tracking_code"`
			BrandListType []interface{} `json:"brand_list_type"`
			IsAmazon      bool          `json:"is_amazon"`
			ShopBrandType int           `json:"shop_brand_type"`
		} `json:"data"`
	} `json:"data"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	V       string `json:"v"`
}
type GetOrderTransactionsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalPage  int    `json:"total_page"`
		TotalTrans string `json:"total_trans"`
		TotalItems string `json:"total_items"`
		Limit      int    `json:"limit"`
		List       []struct {
			Mcid            string `json:"mcid"`
			MerchantName    string `json:"merchant_name"`
			OrderId         string `json:"order_id"`
			OrderTime       string `json:"order_time"`
			SaleAmount      string `json:"sale_amount"`
			SaleComm        string `json:"sale_comm"`
			Status          string `json:"status"`
			NormId          string `json:"norm_id"`
			OriAmount       string `json:"ori_amount"`
			OriAffBrokerage string `json:"ori_aff_brokerage"`
			ProdId          string `json:"prod_id"`
			OrderUnit       string `json:"order_unit"`
			Uid             string `json:"uid"`
			Uid2            string `json:"uid2"`
			Uid3            string `json:"uid3"`
			Uid4            string `json:"uid4"`
			Uid5            string `json:"uid5"`
			ClickRef        string `json:"click_ref"`
			PartnerboostId  string `json:"partnerboost_id"`
			CommRate        string `json:"comm_rate"`
			ValidationDate  string `json:"validation_date"`
			Note            string `json:"note"`
			CustomerCountry string `json:"customer_country"`
			VoucherCode     string `json:"voucher_code"`
			IsDirect        int    `json:"is_direct"`
			ChannelId       string `json:"channel_id"`
			BrandId         string `json:"brand_id"`
			LastUpdateTime  string `json:"last_update_time"`
			PaidStatus      int    `json:"paid_status"`
		} `json:"list"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Code    interface{} `json:"code"`
	Message interface{} `json:"message"`
	Data    struct {
		TotalMcid int         `json:"total_mcid"`
		TotalPage int         `json:"total_page"`
		Limit     interface{} `json:"limit"`
		List      []struct {
			Mcid                string      `json:"mcid"`
			Mid                 int         `json:"mid"`
			BrandId             int         `json:"brand_id"`
			MerchantName        string      `json:"merchant_name"`
			CommRate            string      `json:"comm_rate"`
			CommDetail          string      `json:"comm_detail"`
			SiteUrl             string      `json:"site_url"`
			Logo                string      `json:"logo"`
			Categories          string      `json:"categories"`
			Tags                string      `json:"tags"`
			OfferType           string      `json:"offer_type"`
			NetworkPartner      string      `json:"network_partner"`
			AvgPaymentCycle     int         `json:"avg_payment_cycle"`
			AvgPayout           string      `json:"avg_payout"`
			Country             string      `json:"country"`
			SupportRegion       string      `json:"support_region"`
			BrandStatus         string      `json:"brand_status"`
			MerchantStatus      string      `json:"merchant_status"`
			Datetime            int         `json:"datetime"`
			Relationship        string      `json:"relationship"`
			TrackingUrl         string      `json:"tracking_url"`
			TrackingUrlShort    string      `json:"tracking_url_short"`
			RD                  string      `json:"RD"`
			SiteDesc            string      `json:"site_desc"`
			FilterWords         string      `json:"filter_words"`
			CurrencyName        string      `json:"currency_name"`
			AllowSml            string      `json:"allow_sml"`
			PostAreaList        []string    `json:"post_area_list"`
			RepName             string      `json:"rep_name"`
			RepEmail            string      `json:"rep_email"`
			MlinkHash           string      `json:"mlink_hash"`
			BrandType           string      `json:"brand_type"`
			IsDirect            int         `json:"is_direct"`
			SupportCouponordeal interface{} `json:"support_couponordeal"`
		} `json:"list"`
	} `json:"data"`
}
