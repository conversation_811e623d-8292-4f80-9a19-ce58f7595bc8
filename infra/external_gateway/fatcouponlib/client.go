package fatcouponlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/fatcouponlib/fatcouponvo"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, page int, limit int) (*fatcouponvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	params := map[string]interface{}{
		"pageNum":  strconv.Itoa(page),
		"pageSize": strconv.Itoa(limit),
	}
	headers := map[string]string{
		"Authorization":   token,
		"Origin":          "https://fatcoupon.com",
		"Referer":         "https://fatcoupon.com/",
		"Accept-Language": "en-US,en;q=0.9",
		"Accept":          "*/*",
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(fatcouponvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("fatcouponvo GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit)
		if err != nil {
			zap.L().Error("fatcouponvo BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPages {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *fatcouponvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.Data))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.Data {
		// 处理时间
		formattedDateTime := order.OrderDate.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := order.OrderDate.Format("2006-1-2")
		orderTimeHour := order.OrderDate.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(fmt.Sprintf("%v", order.Status))))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: 平台=%s, 原始状态='%s' -> 使用原状态\n", "fatcoupon", order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    "(" + order.Id,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Store.Name,
			"merchant_id":      order.Store.Id,
			"commission":       order.Cashback,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             "-",
			"tag2":             "",
			"ip":               "",
			"referer_url":      order.ReferrerId,
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   order.Cashback,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}
