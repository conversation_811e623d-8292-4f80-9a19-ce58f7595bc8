package fatcouponvo

import "time"

type GetOrderTransactionsResp struct {
	Errno  int    `json:"errno"`
	Errmsg string `json:"errmsg"`
	Data   struct {
		Count int `json:"count"`
		Data  []struct {
			Type             int         `json:"type"`
			Amount           float64     `json:"amount"`
			UserCashback     float64     `json:"userCashback"`
			IsFirstOrder     bool        `json:"isFirstOrder"`
			UserId           string      `json:"userId"`
			StoreId          string      `json:"storeId"`
			OrderId          string      `json:"orderId"`
			TripId           string      `json:"tripId"`
			ReferrerId       string      `json:"referrerId"`
			Status           int         `json:"status"`
			BonusRate        interface{} `json:"bonusRate"`
			OrderDate        time.Time   `json:"orderDate"`
			CreatedAt        time.Time   `json:"createdAt"`
			NewUserBonusRate int         `json:"newUserBonusRate"`
			Id               string      `json:"id"`
			Cashback         float64     `json:"cashback"`
			Coin             int         `json:"coin"`
			User             struct {
				FirstName string    `json:"firstName"`
				LastName  string    `json:"lastName"`
				Name      string    `json:"name"`
				Email     string    `json:"email"`
				CreatedAt time.Time `json:"createdAt"`
				Id        string    `json:"id"`
			} `json:"user"`
			Referrer struct {
				FirstName string    `json:"firstName"`
				LastName  string    `json:"lastName"`
				Name      string    `json:"name"`
				Email     string    `json:"email"`
				CreatedAt time.Time `json:"createdAt"`
				Id        string    `json:"id"`
			} `json:"referrer"`
			Store struct {
				Name string `json:"name"`
				Id   string `json:"id"`
			} `json:"store"`
			Trip interface{} `json:"trip"`
		} `json:"data"`
		PageNum    int `json:"pageNum"`
		PageSize   int `json:"pageSize"`
		TotalPages int `json:"totalPages"`
	} `json:"data"`
}
