package joingekkovo

// MerchantFeedResponse represents the response from the brand-feed API
type MerchantFeedResponse struct {
	Feed []MerchantFeed `json:"feed"`
}

// MerchantFeed represents a single merchant feed item
type MerchantFeed struct {
	TopOffer            string  `json:"top_offer"`
	TmAllow             string  `json:"tm_allow"`
	Subcategory         string  `json:"subcategory"`
	NetworkRank         *string `json:"networkrank"`
	MerchantName        string  `json:"merchant_name"`
	MerchantId          int     `json:"merchant_id"`
	MerchantDomain      string  `json:"merchant_domain"`
	Geo                 string  `json:"geo"`
	Epc7Days            string  `json:"epc7days"`
	Deeplink            bool    `json:"deeplink"`
	CouponAllow         string  `json:"coupon_allow"`
	CommissionRate      *string `json:"commission_rate"`
	Category            string  `json:"category"`
	AvgPayoutCommission *string `json:"avg_payout_commission"`
}

type GeoListResponse struct {
	Msg   string    `json:"msg"`
	Error string    `json:"error"`
	Data  []GeoItem `json:"data"`
}

// GeoItem represents a single geo item
type GeoItem struct {
	Value string `json:"value"`
	Label string `json:"label"`
}
