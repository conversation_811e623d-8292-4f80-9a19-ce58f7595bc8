package joingekkolib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/joingekkolib/joingekkovo"
	"brand-bidding-service/infra/utils/domainutil"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var GeoList = []string{"us", "au", "ca", "gb", "de", "it", "at", "es", "fr"}

func GetGeoList(ctx context.Context) ([]string, *ecode.ErrCode) {
	headers := map[string]string{
		"accept":          "application/json, text/plain, */*",
		"accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
		"origin":          "https://joingekko.com",
		"referer":         "https://joingekko.com/",
	}

	resp, err := remoteInvokeWithUrl(ctx, geoMasterHost+geoMasterPath, http.MethodGet, nil, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	geoResp := new(joingekkovo.GeoListResponse)
	if err := json.Unmarshal(resp, geoResp); err != nil {
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}

	if geoResp.Error != "" {
		return nil, ecode.With(ecode.ErrCodeHTTP, fmt.Errorf("geo api error: %s", geoResp.Error))
	}

	var geoList []string
	for _, item := range geoResp.Data {
		if item.Value != "all" { // Skip the "all" option
			geoList = append(geoList, item.Value)
		}
	}

	return geoList, nil
}

func generateAuthToken(secretKey string) string {
	// Get current time and subtract 8 hours
	now := time.Now().Add(-8 * time.Hour)
	// Format time as required
	formattedTime := now.Format("2006-01-02 15:04")

	// Generate MD5 hash
	hash := md5.New()
	hash.Write([]byte(formattedTime + secretKey))
	return strings.ToUpper(hex.EncodeToString(hash.Sum(nil)))
}

// GetMerchant fetches merchant data for a specific geo region
func GetMerchant(geo string, secretKey string, publisherKey string, propertyID string, authKey string) (*joingekkovo.MerchantFeedResponse, *ecode.ErrCode) {
	ctx := context.Background()
	authToken := generateAuthToken(secretKey)

	params := map[string]interface{}{
		"publisherkey": publisherKey,
		"propertyid":   propertyID,
		"authkey":      authKey,
		"authtoken":    authToken,
		"geo":          geo,
	}

	headers := map[string]string{
		"Content-Type":     "application/json",
		"content-encoding": "gzip",
		"Accept":           "application/json",
	}

	// Add small delay to prevent rate limiting
	time.Sleep(10 * time.Millisecond)

	resp, err := remoteInvokeWithUrl(ctx, host+apiMerchantFeed, http.MethodGet, params, headers, nil)
	fmt.Println("resp: ", string(resp))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	merchantResp := new(joingekkovo.MerchantFeedResponse)
	if err := json.Unmarshal(resp, merchantResp); err != nil {
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}

	return merchantResp, nil
}

// BatchGetMerchants fetches and processes merchant data for all supported geos
func BatchGetMerchants(secretKey string, publisherKey string, propertyID string, authKey string, userName string, accountName string) ([]map[string]interface{}, *ecode.ErrCode) {
	createDataRows := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	geoList, err := GetGeoList(context.Background())
	if err != nil {
		geoList = GeoList
	}
	for i, geo := range geoList {
		fmt.Println(geo, i, len(geoList))
		resp, err := GetMerchant(geo, secretKey, publisherKey, propertyID, authKey)
		if err != nil {
			continue
		}
		for _, row := range resp.Feed {
			uniqueKey := strconv.Itoa(row.MerchantId)
			if _, exists := uniqueMap[uniqueKey]; !exists {
				// Handle nil pointer values
				commissionRate := ""
				if row.CommissionRate != nil {
					commissionRate = *row.CommissionRate
				}

				merchantRow := map[string]interface{}{
					"merchant_id":         strconv.Itoa(row.MerchantId),
					"merchant_slug":       row.MerchantName,
					"merchant_name":       row.MerchantName,
					"category_name":       row.Category,
					"country":             row.Geo,
					"supported_countries": row.Geo,
					"website":             domainutil.ExtractDomain(row.MerchantDomain),
					"original_domain":     row.MerchantDomain,
					"cashback_info":       commissionRate,
					"track_url":           row.Deeplink,
					"sub1":                "subid",
					"status":              1,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypeJoinGekko,
					"created_at":          time.Now().Format("2006-01-02"),
					"updated_at":          time.Now().Format("2006-01-02"),
				}
				createDataRows = append(createDataRows, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}
	}

	return createDataRows, nil
}
