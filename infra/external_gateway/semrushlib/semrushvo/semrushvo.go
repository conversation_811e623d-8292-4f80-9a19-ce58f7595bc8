package semrushvo

type OrganicSummaryResp struct {
	Jsonrpc string                        `json:"jsonrpc"`
	Id      int                           `json:"id"`
	Result  []*SingleDomainOrganicSummary `json:"result"`
}

type SingleDomainOrganicSummary struct {
	AdwordsPositions              int    `json:"adwordsPositions"`
	AdwordsTraffic                int    `json:"adwordsTraffic"`
	AdwordsTrafficCost            int    `json:"adwordsTrafficCost"`
	Database                      string `json:"database"`
	OrganicPositions              int    `json:"organicPositions"`
	OrganicTraffic                int    `json:"organicTraffic"`
	OrganicTrafficBranded         int    `json:"organicTrafficBranded"`
	OrganicTrafficCost            int    `json:"organicTrafficCost"`
	OrganicTrafficNonBranded      int    `json:"organicTrafficNonBranded"`
	Positions                     int    `json:"positions"`
	Rank                          int    `json:"rank"`
	SerpFeaturesPositions         int    `json:"serpFeaturesPositions"`
	SerpFeaturesTraffic           int    `json:"serpFeaturesTraffic"`
	SerpFeaturesTrafficBranded    int    `json:"serpFeaturesTrafficBranded"`
	SerpFeaturesTrafficCost       int    `json:"serpFeaturesTrafficCost"`
	SerpFeaturesTrafficNonBranded int    `json:"serpFeaturesTrafficNonBranded"`
	Traffic                       int    `json:"traffic"`
	TrafficBranded                int    `json:"trafficBranded"`
	TrafficCost                   int    `json:"trafficCost"`
	TrafficNonBranded             int    `json:"trafficNonBranded"`
}

type T struct {
	Jsonrpc string `json:"jsonrpc"`
	Id      int    `json:"id"`
	Result  []struct {
		AdwordsPositions               int    `json:"adwordsPositions"`
		AdwordsPositionsTrend          []int  `json:"adwordsPositionsTrend"`
		AdwordsTraffic                 int    `json:"adwordsTraffic"`
		AdwordsTrafficCost             int    `json:"adwordsTrafficCost"`
		Date                           string `json:"date"`
		IntentCommercialPositions      int    `json:"intentCommercialPositions"`
		IntentCommercialTraffic        int    `json:"intentCommercialTraffic"`
		IntentCommercialTrafficCost    int    `json:"intentCommercialTrafficCost"`
		IntentInformationalPositions   int    `json:"intentInformationalPositions"`
		IntentInformationalTraffic     int    `json:"intentInformationalTraffic"`
		IntentInformationalTrafficCost int    `json:"intentInformationalTrafficCost"`
		IntentNavigationalPositions    int    `json:"intentNavigationalPositions"`
		IntentNavigationalTraffic      int    `json:"intentNavigationalTraffic"`
		IntentNavigationalTrafficCost  int    `json:"intentNavigationalTrafficCost"`
		IntentTransactionalPositions   int    `json:"intentTransactionalPositions"`
		IntentTransactionalTraffic     int    `json:"intentTransactionalTraffic"`
		IntentTransactionalTrafficCost int    `json:"intentTransactionalTrafficCost"`
		IntentUnknownPositions         int    `json:"intentUnknownPositions"`
		IntentUnknownTraffic           int    `json:"intentUnknownTraffic"`
		IntentUnknownTrafficCost       int    `json:"intentUnknownTrafficCost"`
		OrganicPositions               int    `json:"organicPositions"`
		OrganicPositionsBranded        int    `json:"organicPositionsBranded"`
		OrganicPositionsTrend          []int  `json:"organicPositionsTrend"`
		OrganicTraffic                 int    `json:"organicTraffic"`
		OrganicTrafficBranded          int    `json:"organicTrafficBranded"`
		OrganicTrafficCost             int    `json:"organicTrafficCost"`
		OrganicTrafficNonBranded       int    `json:"organicTrafficNonBranded"`
		Positions                      int    `json:"positions"`
		PositionsBranded               int    `json:"positionsBranded"`
		Rank                           int    `json:"rank"`
		SerpFeaturesPositions          int    `json:"serpFeaturesPositions"`
		SerpFeaturesPositionsBranded   int    `json:"serpFeaturesPositionsBranded"`
		SerpFeaturesTraffic            int    `json:"serpFeaturesTraffic"`
		SerpFeaturesTrafficBranded     int    `json:"serpFeaturesTrafficBranded"`
		SerpFeaturesTrafficCost        int    `json:"serpFeaturesTrafficCost"`
		SerpFeaturesTrafficNonBranded  int    `json:"serpFeaturesTrafficNonBranded"`
		Traffic                        int    `json:"traffic"`
		TrafficBranded                 int    `json:"trafficBranded"`
		TrafficCost                    int    `json:"trafficCost"`
		TrafficNonBranded              int    `json:"trafficNonBranded"`
	} `json:"result"`
}
