package semrushlib

import (
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/semrushlib/semrushvo"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"sort"
)

func GetOrganicSummary(domain string) (*semrushvo.OrganicSummaryResp, *ecode.ErrCode) {
	ctx := context.Background()
	// Generate random ID between 10 and 50
	payloadId := generateRandomID(10, 50)
	// Generate request ID
	payloadRequestId := fmt.Sprintf("%s-%s-%s-%s-%s",
		generateRequestID(8), generateRequestID(4), generateRequestID(4),
		generateRequestID(4), generateRequestID(12))

	params := map[string]interface{}{}

	headers := map[string]string{}
	// Create the payload as a map
	payload := map[string]interface{}{
		"id":      payloadId,
		"jsonrpc": "2.0",
		"method":  "organic.Summary",
		"params": map[string]interface{}{
			"request_id": payloadRequestId,
			"report":     "domain.overview",
			"args": map[string]interface{}{
				"database":   "us",
				"dateFormat": "date",
				"dateType":   "monthly",
				"global":     true,
				"searchItem": domain,
				"searchType": "domain",
			},
			"userId": userId,
			"apiKey": apiKey,
		},
	}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	resp, err := remoteInvokeWithUrl(ctx, host+apiRpc, http.MethodPost, params, headers, bytes.NewBuffer(payloadBytes))
	if err != nil {
		fmt.Println(resp)
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	organicSummaryResp := new(semrushvo.OrganicSummaryResp)
	err = json.Unmarshal(resp, organicSummaryResp)
	if err != nil {
		zap.L().Error("semrushlib GetOrganicSummary json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return organicSummaryResp, nil
}

func FindTopCountries(data *semrushvo.OrganicSummaryResp) []*semrushvo.SingleDomainOrganicSummary {
	// Sort result by AdwordsTraffic in descending order
	sort.SliceStable(data.Result, func(i, j int) bool {
		return data.Result[i].AdwordsTraffic > data.Result[j].AdwordsTraffic
	})

	// First pass: Select up to two countries based on AdwordsTraffic
	selected := make([]*semrushvo.SingleDomainOrganicSummary, 0)
	uniqueDatabases := make(map[string]bool)

	for _, entry := range data.Result {
		if len(selected) >= 2 {
			break
		}
		if !uniqueDatabases[entry.Database] && entry.AdwordsTraffic > 0 {
			selected = append(selected, entry)
			uniqueDatabases[entry.Database] = true
		}
	}

	// Check if we have less than two countries selected
	if len(selected) < 2 {
		// Sort again by Traffic in descending order for backup selection
		sort.SliceStable(data.Result, func(i, j int) bool {
			return data.Result[i].Traffic > data.Result[j].Traffic
		})

		// Second pass: Select based on Traffic to fill the remaining spots
		for _, entry := range data.Result {
			if len(selected) >= 2 {
				break
			}
			if !uniqueDatabases[entry.Database] && entry.Traffic > 0 {
				selected = append(selected, entry)
				uniqueDatabases[entry.Database] = true
			}
		}
	}

	return selected
}
