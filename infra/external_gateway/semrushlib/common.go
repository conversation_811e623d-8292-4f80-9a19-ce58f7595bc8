package semrushlib

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"strings"
	"time"
)

const (
	host   = "https://vip5.semrush.fun"
	apiKey = "333333333"
	userId = 111111111
	apiRpc = "/dpa/rpc"
)

const (
	requestInterval = time.Second * 3
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时3000ms
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	if strings.Contains(baseUrl, "?") {
		fullUrl = fmt.Sprintf("%s&%s", baseUrl, urlValues.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")
	req.Header.Set("Cookie", "_pd=2260; ref_code=__default__; refer_source=\"\"; _tt_enable_cookie=1; _ttp=01K2W52KNXSE3XWTB29FRNN7SY_.tt.1; _fbp=fb.1.1755438993144.197201579827846041; _mkto_trk=id:519-IIY-869&token:_mch-semrush.fun-3f85d0274fc8663713283a35702f403; __51vcke__KqcKhFOyfdc0dHSJ=fd2c4469-b434-5881-b0d8-480267aa83d1; __51vuft__KqcKhFOyfdc0dHSJ=1755439248275; _ampl=7WkyI7YnZ-2f2w580IxY0; _zitok=61eb129bedffb08e6e761755439264; _ld=15; rbuid=rbos-91febb09-1b65-4ea3-a392-788a0a8c5094; _gcl_au=1.1.456901568.1755438994.1436741648.1756438417.1756438417; cf_clearance=8r0F3mADT_CIa8319LZa3muqWBrZjVIf.euqW4Daw84-1756867949-1.2.1.1-rIFt_gFLX59g4YCqCp8nZr.lRPnAY.7qj8E6FMMRSdVMlCMGPUYw_R.APqPfLqpGSJe5.2rzyZT2tu4.O3gafoE0GobmBh3vu1zNlmo.AHUbgNsc4Rvnv8AQiCmOe9qatQVMj93GZQodw7MWXBllzEe_zq5M3oOGj9taSSaxPLwesWNzKuHCZ6E7GZVhnAbDFk9hJUvMTRl7o5EWT3VLFrdJRWIq3JQaHTCEp7_l3_I; _cd=1c7aa0f8-62a6-43af-b42f-12b8668ec9fc; __51uvsct__KqcKhFOyfdc0dHSJ=20; lux_uid=175686796046769650; _gid=GA1.2.793539883.1756867961; _dc_gtm_UA-6197637-22=1; __vtins__KqcKhFOyfdc0dHSJ=%7B%22sid%22%3A%20%22a209dce3-777a-5942-ab3f-5c5cfc3c1f10%22%2C%20%22vd%22%3A%202%2C%20%22stt%22%3A%2011082%2C%20%22dr%22%3A%2011082%2C%20%22expires%22%3A%201756869768139%2C%20%22ct%22%3A%201756867968139%7D; ttcsid=1756867962330::BEaFLXW39BpNIo544vCB.23.1756867969144; ttcsid_C4FPRRVM9G8R5RJ0MPKG=1756867962330::Ljem4iUrnH8ij2-0knqE.23.1756867969355; ttcsid_D1N4AQJC77U6G0ESFLB0=1756867962331::UwJE-qpCXTmLjqG-3Yn2.23.1756867969355; _uetsid=0f467780887111f08c5f9d7a26e7d330|1ovtjuu|2|fz0|0|2072; _uetvid=0f0ec3b0c11e11eeadf15ddeb697822f|1eudv7z|1756867969808|1|1|bat.bing.com/p/insights/c/n; _ga=GA1.2.2131970364.1755438994; _ga_BPNLXP3JQG=GS2.1.s1756867958$o14$g1$t1756867974$j44$l0$h204186602; _ga_HYWKMHR981=GS2.1.s1756867958$o14$g1$t1756867974$j44$l0$h0")
	req.Header.Set("Origin", "host")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0")
	req.Header.Set("origin", host)
	//req.Header.Set("referer", host+"/analytics/overview/?searchType=domain&__gmitm=aLZ9pYTAT7M9kEk3p8c4rwX5hNKzd87")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

// generateRandomID generates a random integer ID between min and max.
func generateRandomID(min, max int) int {
	rand.Seed(time.Now().UnixNano())
	return rand.Intn(max-min+1) + min
}

// generateRequestID generates a random string ID with specified length.
func generateRequestID(length int) string {
	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
