package exchangeratelib

import (
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/exchangeratelib/exchangeratevo"
	"context"
	"encoding/json"
	"go.uber.org/zap"
	"net/http"
)

func GetExchangeRates() (*exchangeratevo.GetExchangeRatesResp, *ecode.ErrCode) {
	ctx := context.Background()
	params := map[string]interface{}{}
	headers := map[string]string{
		"Accept-Language": "en-US,en;q=0.9",
		"Accept":          "*/*",
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetRates, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getExchangeRatesResp := new(exchangeratevo.GetExchangeRatesResp)
	err = json.Unmarshal(resp, getExchangeRatesResp)
	if err != nil {
		zap.L().Error("exchangeratelib GetExchangeRates json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getExchangeRatesResp, nil
}
