package eclicklinkvo

type GetOrderTransactionsResp struct {
	Conversions []struct {
		ConversionId            string  `json:"conversion_id"`
		ConversionUnixTimestamp int     `json:"conversion_unix_timestamp"`
		Sub1                    string  `json:"sub1"`
		Sub2                    string  `json:"sub2"`
		Sub3                    string  `json:"sub3"`
		Sub4                    string  `json:"sub4"`
		Sub5                    string  `json:"sub5"`
		SourceId                string  `json:"source_id"`
		RevenueType             string  `json:"revenue_type"`
		Revenue                 float64 `json:"revenue"`
		SessionUserIp           string  `json:"session_user_ip"`
		ConversionUserIp        string  `json:"conversion_user_ip"`
		Country                 string  `json:"country"`
		Region                  string  `json:"region"`
		City                    string  `json:"city"`
		Dma                     int     `json:"dma"`
		Carrier                 string  `json:"carrier"`
		Platform                string  `json:"platform"`
		OsVersion               string  `json:"os_version"`
		DeviceType              string  `json:"device_type"`
		Brand                   string  `json:"brand"`
		Browser                 string  `json:"browser"`
		Language                string  `json:"language"`
		HttpUserAgent           string  `json:"http_user_agent"`
		IsEvent                 bool    `json:"is_event"`
		Event                   string  `json:"event"`
		TransactionId           string  `json:"transaction_id"`
		ClickUnixTimestamp      int     `json:"click_unix_timestamp"`
		Isp                     string  `json:"isp"`
		Referer                 string  `json:"referer"`
		AppId                   string  `json:"app_id"`
		Idfa                    string  `json:"idfa"`
		IdfaMd5                 string  `json:"idfa_md5"`
		IdfaSha1                string  `json:"idfa_sha1"`
		GoogleAdId              string  `json:"google_ad_id"`
		GoogleAdIdMd5           string  `json:"google_ad_id_md5"`
		GoogleAdIdSha1          string  `json:"google_ad_id_sha1"`
		AndroidId               string  `json:"android_id"`
		AndroidIdMd5            string  `json:"android_id_md5"`
		AndroidIdSha1           string  `json:"android_id_sha1"`
		CurrencyId              string  `json:"currency_id"`
		IsViewThrough           bool    `json:"is_view_through"`
		OrderId                 string  `json:"order_id"`
		Adv1                    string  `json:"adv1"`
		Adv2                    string  `json:"adv2"`
		Adv3                    string  `json:"adv3"`
		Adv4                    string  `json:"adv4"`
		Adv5                    string  `json:"adv5"`
		Relationship            struct {
			Offer struct {
				NetworkOfferId          int    `json:"network_offer_id"`
				NetworkId               int    `json:"network_id"`
				Name                    string `json:"name"`
				OfferStatus             string `json:"offer_status"`
				NetworkTrackingDomainId int    `json:"network_tracking_domain_id"`
			} `json:"offer"`
			EventsCount int `json:"events_count"`
		} `json:"relationship"`
		SaleAmount float64 `json:"sale_amount"`
		CouponCode string  `json:"coupon_code"`
	} `json:"conversions"`
	Paging struct {
		Page       int `json:"page"`
		PageSize   int `json:"page_size"`
		TotalCount int `json:"total_count"`
	} `json:"paging"`
}
