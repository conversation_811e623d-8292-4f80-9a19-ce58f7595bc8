package eclicklinklib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/eclicklinklib/eclicklinkvo"
	"brand-bidding-service/infra/external_gateway/emaillib"
	"brand-bidding-service/infra/external_gateway/emaillib/emailvo"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*eclicklinkvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-1-2")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-1-2")

	params := map[string]interface{}{
		"page":      strconv.Itoa(page),
		"page_size": strconv.Itoa(limit),
	}

	headers := map[string]string{
		"x-eflow-api-key": token,
		"Content-Type":    "application/json",
	}

	requestBody := map[string]interface{}{
		"timezone_id":      20,
		"from":             beginDate,
		"to":               endDate,
		"show_events":      true,
		"show_conversions": true,
		"query": map[string]interface{}{
			"filters":      []string{}, // 空过滤器数组
			"search_terms": []string{}, // 空搜索术语数组
		},
	}
	// 将请求体编码为 JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(eclicklinkvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("blueafflib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int, exchangeRatesUsdMap map[string]float64) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("blueafflib GetOrderTransactionsByPortal GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp, exchangeRatesUsdMap)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page*limit >= getOrderTransactionsResp.Paging.TotalCount {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *eclicklinkvo.GetOrderTransactionsResp, exchangeRatesUsdMap map[string]float64) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Conversions))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Conversions {
		tagCode := order.Sub1
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t := time.Unix(int64(order.ConversionUnixTimestamp), 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()

		// 计算汇率
		commissionUsd := float64(0)
		if _, ok := exchangeRatesUsdMap[order.CurrencyId]; !ok {
			// 发送邮件，补充该汇率
			emailConfig := new(emailvo.EmailConfig)
			emailConfig.From = "<EMAIL>"
			emailConfig.Password = "UOYDSUVBLWKOZEEN"
			emailConfig.Host = "smtp.163.com"
			emailConfig.Port = "25"
			toEmailList := make([]string, 0)
			toEmail := "<EMAIL>"
			toEmailList = append(toEmailList, toEmail)
			emaillib.SendEmail(emailConfig, toEmailList, "今日佣金报告-汇率缺失！！！", "缺失汇率："+order.CurrencyId)
			continue
		}
		if order.CurrencyId == "USD" {
			commissionUsd = order.Revenue
		} else {
			commissionUsd = order.Revenue * exchangeRatesUsdMap[order.CurrencyId]
		}
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace("pending")))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, "pending")
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.ConversionId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Relationship.Offer.Name,
			"merchant_id":      order.Relationship.Offer.NetworkOfferId,
			"commission":       order.Revenue,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Sub2,
			"ip":               order.ConversionUserIp,
			"referer_url":      order.TransactionId,
			"customer_country": order.Country,
			"currency":         order.CurrencyId,
			"commission_usd":   commissionUsd,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}
