package joinhoneylib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/joinhoneylib/joinhoneyvo"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"
	"time"
)

func GetOrderTransactions(dirPath string) (map[string]*joinhoneyvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	result := make(map[string]*joinhoneyvo.GetOrderTransactionsResp)

	// Read directory contents
	files, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	// Iterate through files
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".json") {
			// Read file content
			filePath := filepath.Join(dirPath, file.Name())
			data, err := ioutil.ReadFile(filePath)
			if err != nil {
				// Skip file on read error and continue with next file
				continue
			}

			// Check if file is empty
			if len(data) == 0 {
				// Skip empty file and continue with next file
				continue
			}

			// Parse JSON
			var resp joinhoneyvo.GetOrderTransactionsResp
			if err := json.Unmarshal(data, &resp); err != nil {
				// Skip invalid JSON and continue with next file
				continue
			}

			// Add to map with filename (without .json extension) as key
			filename := strings.TrimSuffix(file.Name(), ".json")
			result[filename] = &resp
		}
	}

	return result, nil
}

func BatchGetOrderTransactions(dirPath string) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	// 调用 GetOrderTransactions 函数
	orderTransactionsMap, err := GetOrderTransactions(dirPath)
	if err != nil {
		return allOrderList, err
	}

	for accountName, order := range orderTransactionsMap {
		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, order)...)
	}
	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *joinhoneyvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.GetTransactionsByUserId))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.GetTransactionsByUserId {
		if order.Store == nil {
			continue
		}
		if order.Type == "PAYOUT" {
			continue
		}
		tagCode := "-"
		orderStatus := "PENDING"
		if order.State == "LOCKED" && order.Value != 0 {
			orderStatus = "paid"
		} else if order.State == "LOCKED" && order.Value == 0 {
			orderStatus = "rejected"
		}
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(orderStatus)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, orderStatus)
		}
		// 转换为 time.Time
		t := time.Unix(int64(order.Created/1000), 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		orderMap := map[string]interface{}{
			"conversion_id":    order.TransactionId,
			"account":          accountName,
			"order_id":         order.TransactionId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Store.StoreId,
			"merchant_id":      order.Store.StoreId,
			"commission":       float64(order.Value) / 100.0,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             "",
			"ip":               "",
			"referer_url":      order.ReferredUser,
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   float64(order.Value) / 100.0,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}
