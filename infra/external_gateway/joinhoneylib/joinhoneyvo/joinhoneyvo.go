package joinhoneyvo

type GetOrderTransactionsResp struct {
	Data struct {
		GetTransactionsByUserId []struct {
			TransactionId string `json:"transactionId"`
			UserId        string `json:"userId"`
			Value         int    `json:"value"`
			State         string `json:"state"`
			Created       int64  `json:"created"`
			Updated       int64  `json:"updated"`
			Type          string `json:"type"`
			Locked        *int64 `json:"locked"`
			Store         *struct {
				Name                 string `json:"name"`
				StoreId              string `json:"storeId"`
				CurrentCashbackOffer struct {
					IsFlatFee bool `json:"isFlatFee"`
				} `json:"currentCashbackOffer"`
				Country string `json:"country"`
			} `json:"store"`
			ProductOfferActivation interface{} `json:"productOfferActivation"`
			PayoutGifts            []struct {
				PayoutGiftId    string `json:"payoutGiftId"`
				PayoutStoreInfo struct {
					Store struct {
						Name    string `json:"name"`
						StoreId string `json:"storeId"`
					} `json:"store"`
					PayoutProvider string `json:"payoutProvider"`
					Currency       string `json:"currency"`
				} `json:"payoutStoreInfo"`
			} `json:"payoutGifts"`
			ReferredUser               interface{} `json:"referredUser"`
			AffiliateSaleValue         *int        `json:"affiliateSaleValue"`
			AffiliateSaleValueAtLocked *int        `json:"affiliateSaleValueAtLocked"`
			AffiliateGivenPercent      *float64    `json:"affiliateGivenPercent"`
			AffiliateExternalCreated   *int64      `json:"affiliateExternalCreated"`
			AffiliatePartials          *string     `json:"affiliatePartials"`
			CheckoutMinPercent         interface{} `json:"checkoutMinPercent"`
			CheckoutMaxPercent         interface{} `json:"checkoutMaxPercent"`
			CheckoutSeen               interface{} `json:"checkoutSeen"`
			ManualDescription          interface{} `json:"manualDescription"`
			OnboardingStep             interface{} `json:"onboardingStep"`
		} `json:"getTransactionsByUserId"`
	} `json:"data"`
}
