package fatpartnervo

import "time"

type GetOrderTransactionsResp struct {
	Message string `json:"message"`
	Data    struct {
		Count int `json:"count"`
		Page  int `json:"page"`
		Size  int `json:"size"`
		Total int `json:"total"`
		Data  []struct {
			Id          int         `json:"id"`
			StoreId     int         `json:"storeId"`
			OrderId     string      `json:"orderId"`
			OrderStatus string      `json:"orderStatus"`
			ClickId     string      `json:"clickId"`
			OrderedAt   string      `json:"orderedAt"`
			CreatedAt   string      `json:"createdAt"`
			UpdatedAt   string      `json:"updatedAt"`
			ApprovedAt  interface{} `json:"approvedAt"`
			CancelledAt interface{} `json:"cancelledAt"`
			Amount      float64     `json:"amount"`
			Commission  float64     `json:"commission"`
			StoreName   string      `json:"storeName"`
			SubId1      string      `json:"subId1"`
			SubId2      string      `json:"subId2"`
			SubId3      string      `json:"subId3"`
			SubId4      string      `json:"subId4"`
			SubId5      string      `json:"subId5"`
		} `json:"data"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Data struct {
		Count int `json:"count"`
		Page  int `json:"page"`
		Size  int `json:"size"`
		Total int `json:"total"`
		Data  []struct {
			Id                int       `json:"id"`
			CashbackType      string    `json:"cashbackType"`
			Domain            string    `json:"domain"`
			CreatedAt         time.Time `json:"createdAt"`
			UpdatedAt         time.Time `json:"updatedAt"`
			AffCommissionRate string    `json:"affCommissionRate"`
			DeepLink          string    `json:"deepLink"`
			Image             string    `json:"image"`
			Country           string    `json:"country"`
			Name              string    `json:"name"`
			Description       string    `json:"description"`
			Geo               string    `json:"geo"`
			CashbackDesc      string    `json:"cashbackDesc"`
			Urls              []struct {
				StoreId  int    `json:"storeId"`
				Position int    `json:"position"`
				Domain   string `json:"domain"`
			} `json:"urls"`
		} `json:"data"`
	} `json:"data"`
	Message string `json:"message"`
}
