package fatpartnerlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/fatpartnerlib/fatpartnervo"
	"brand-bidding-service/infra/utils/domainutil"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*fatpartnervo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{
		"dateBy":    "ordered_at",
		"startedAt": string(beginDate),
		"endedAt":   string(endDate),
		"page":      strconv.Itoa(page),
		"size":      strconv.Itoa(limit),
	}
	if len(status) > 0 {
		params["status"] = status
	}

	headers := map[string]string{
		"Authorization": "Basic " + token,
		"Content-Type":  "application/json;charset=utf-8",
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(fatpartnervo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("fatpartnerlib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("fatpartnerlib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page*limit >= getOrderTransactionsResp.Data.Total {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *fatpartnervo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.Data))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.Data {
		tagCode := order.SubId1
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t, err := time.Parse("2006-01-02T15:04:05Z", order.OrderedAt)
		if err != nil {
			fmt.Println("Error parsing date:", err)
		}

		// 格式化输出
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.OrderStatus)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.OrderStatus)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.Id,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.StoreId,
			"merchant_id":      order.StoreId,
			"commission":       order.Commission,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.SubId2,
			"ip":               "",
			"referer_url":      order.ClickId,
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   order.Commission,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchants(token string, limit int, page int) (*fatpartnervo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{
		"size": strconv.Itoa(limit),
		"page": strconv.Itoa(page),
	}

	headers := map[string]string{
		"Authorization": "Basic " + token,
		"Content-Type":  "application/json;charset=utf-8",
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetMerchants, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getMerchantsResp := new(fatpartnervo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsResp, nil
}

func BatchGetMerchants(token string, limit int, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page)
		if err != nil {
			zap.L().Error("fatpartnerlib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.Data {
			uniqueKey := strconv.Itoa(merchant.Id)
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         strconv.Itoa(merchant.Id),
					"merchant_slug":       merchant.Name,
					"merchant_name":       merchant.Name,
					"category_name":       "",
					"country":             merchant.Country,
					"supported_countries": merchant.Geo,
					"website":             domainutil.ExtractDomain(merchant.Domain),
					"original_domain":     merchant.Domain,
					"cashback_info":       merchant.CashbackDesc,
					"track_url":           merchant.DeepLink,
					"sub1":                "subId1",
					"status":              1,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypeFatPartner,
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
				}
				allMerchants = append(allMerchants, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page*limit >= getMerchantsResp.Data.Count {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}
