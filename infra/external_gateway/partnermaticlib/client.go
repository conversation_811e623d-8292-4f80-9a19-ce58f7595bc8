package partnermaticlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/partnermaticlib/partnermaticvo"
	"brand-bidding-service/infra/utils/domainutil"
	"brand-bidding-service/infra/utils/safeutil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

func GetToken(account string, password string) (*partnermaticvo.GetTokenResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{}
	requestBody := map[string]interface{}{
		"appId": 32,
		"req": map[string]interface{}{
			"header": map[string]interface{}{
				"token": "",
			},
			"fields":     []interface{}{},
			"attributes": map[string]interface{}{},
			"filter": map[string]interface{}{
				"platform_code": "",
				"account":       account,
				"password":      password,
			},
		},
	}
	json<PERSON><PERSON>, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCode<PERSON><PERSON>, err)
	}
	headers := map[string]string{
		"accept":             "application/json, text/plain, */*",
		"accept-language":    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
		"cache-control":      "no-cache",
		"content-type":       "application/json",
		"origin":             "https://app.partnermatic.com",
		"pragma":             "no-cache",
		"priority":           "u=1, i",
		"referer":            "https://app.partnermatic.com/",
		"sec-ch-ua":          `"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"`,
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": `"macOS"`,
		"sec-fetch-dest":     "empty",
		"sec-fetch-mode":     "cors",
		"sec-fetch-site":     "same-site",
		"user-agent":         "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
	}
	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetToken, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	getTokenResp := new(partnermaticvo.GetTokenResp)
	err = json.Unmarshal(resp, getTokenResp)
	if err != nil {
		zap.L().Error("partnermaticvo GetToken json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getTokenResp, nil
}

func GetOrderTransactions(token string, status string, page int, limit int, startDay int, endDay int) (*partnermaticvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{}

	headers := map[string]string{}

	requestBody := map[string]interface{}{
		"source":    "partnermatic",
		"token":     token,
		"beginDate": string(beginDate),
		"endDate":   string(endDate),
		"curPage":   strconv.Itoa(page),
		"perPage":   strconv.Itoa(limit),
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetOrderTransactions, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(partnermaticvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, status string, page int, limit int, startDay int, endDay int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, status, page, limit, startDay, endDay)
		if err != nil {
			zap.L().Error("partnerboostlib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsResp.Data.TotalPage {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *partnermaticvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.List))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.List {
		tagCode := order.Uid
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 转换为 time.Time
		t := time.Unix(int64(order.OrderTime), 0)
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.PartnerboostId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.Mcid,
			"merchant_id":      order.BrandId,
			"commission":       order.SaleComm,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Uid2,
			"ip":               "",
			"referer_url":      order.ClickRef,
			"customer_country": order.CustomerCountry,
			"currency":         "USD",
			"commission_usd":   order.SaleComm,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}

func GetMerchants(token string, limit int, page int, relationship string) (*partnermaticvo.GetMerchantsResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{}
	requestBody := map[string]interface{}{
		"source":  "partnermatic",
		"token":   token,
		"curPage": strconv.Itoa(page),
		"perPage": strconv.Itoa(limit),
	}
	if len(relationship) > 0 {
		requestBody["relationship"] = relationship
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetMerchants, http.MethodPost, params, map[string]string{}, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	getMerchantsResp := new(partnermaticvo.GetMerchantsResp)
	err = json.Unmarshal(resp, getMerchantsResp)
	if err != nil {
		zap.L().Error("partnermaticvo GetMerchants json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsResp, nil
}

func BatchGetMerchants(token string, limit int, relationship string, accountName string, userName string) ([]map[string]interface{}, *ecode.ErrCode) {
	allMerchants := make([]map[string]interface{}, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	formattedDate := time.Now().Format("2006-01-02")

	for {
		// 调用 GetMerchants 函数
		getMerchantsResp, err := GetMerchants(token, limit, page, relationship)
		if err != nil {
			fmt.Println("partnerboostlib BatchGetMerchants GetMerchants failed", zap.Error(err))
			return allMerchants, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsResp.Data.List {
			uniqueKey := merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantMap := map[string]interface{}{
					"merchant_id":         strconv.Itoa(merchant.BrandId),
					"merchant_slug":       merchant.Mcid,
					"merchant_name":       merchant.MerchantName,
					"category_name":       merchant.Categories,
					"country":             merchant.Country,
					"supported_countries": merchant.SupportRegion,
					"website":             domainutil.ExtractDomain(merchant.SiteUrl),
					"original_domain":     merchant.SiteUrl,
					"cashback_info":       merchant.CommDetail,
					"track_url":           merchant.TrackingUrl,
					"sub1":                "uid",
					"status":              merchant.Relationship,
					"user_name":           userName,
					"account":             accountName,
					"platform_type":       constant.AccountTypePm,
					"created_at":          formattedDate,
					"updated_at":          formattedDate,
				}
				allMerchants = append(allMerchants, merchantMap)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page >= getMerchantsResp.Data.TotalPage {
			break
		}
		fmt.Println("pb: ", page, getMerchantsResp.Data.TotalPage)
		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchants, nil
}

func GetAndApplyNewMerchants(apiToken, account, password string, limit int, accountName string, uuIds string) *ecode.ErrCode {
	// 获取 token
	getToken, err := GetToken(account, password)
	if err != nil {
		return ecode.With(ecode.ErrCodeHTTP, err)
	}
	token := getToken.Data.AuthToken
	// 获取所有没有关系的商家
	merchants, err := BatchGetMerchants(apiToken, limit, "No Relationship", accountName, "")
	if err != nil {
		return err
	}
	fmt.Println(account, len(merchants))
	// 找出未申请的商家ID
	var unappliedMerchantIds []string
	for _, merchant := range merchants {
		if merchantId := safeutil.SafeString(merchant["merchant_slug"]); merchantId != "" {
			unappliedMerchantIds = append(unappliedMerchantIds, merchantId)
		}
	}

	// 如果有未申请的商家，进行申请
	if len(unappliedMerchantIds) > 0 {
		if err := ApplyMerchants(token, unappliedMerchantIds, uuIds); err != nil {
			return err
		}
	}
	return nil
}

func ApplyMerchants(token string, merchantIds []string, uuIds string) *ecode.ErrCode {
	ctx := context.Background()

	// 将商家ID列表按100个一组分割
	chunks := make([][]string, 0)
	for i := 0; i < len(merchantIds); i += 100 {
		end := i + 100
		if end > len(merchantIds) {
			end = len(merchantIds)
		}
		chunks = append(chunks, merchantIds[i:end])
	}

	headers := map[string]string{
		"accept":             "application/json, text/plain, */*",
		"accept-language":    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
		"cache-control":      "no-cache",
		"content-type":       "application/json",
		"origin":             "https://app.partnermatic.com",
		"pragma":             "no-cache",
		"priority":           "u=1, i",
		"referer":            "https://app.partnermatic.com/",
		"sec-ch-ua":          `"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"`,
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": `"macOS"`,
		"sec-fetch-dest":     "empty",
		"sec-fetch-mode":     "cors",
		"sec-fetch-site":     "same-site",
		"user-agent":         "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
	}

	// 对每一组商家ID进行申请
	for _, chunk := range chunks {
		requestBody := map[string]interface{}{
			"appId": 32,
			"req": map[string]interface{}{
				"header": map[string]interface{}{
					"token": token,
				},
				"fields":     []interface{}{},
				"attributes": map[string]interface{}{},
				"filter": map[string]string{
					"mcids":   strings.Join(chunk, ","),
					"c_uuids": uuIds,
				},
			},
		}
		jsonBody, err := json.Marshal(requestBody)
		if err != nil {
			continue
		}
		_, err = remoteInvokeWithUrl(ctx, hostByApi+apiBatchJoin, http.MethodPost, map[string]interface{}{}, headers, bytes.NewBuffer(jsonBody))
		if err != nil {
			continue
		}

		// 根据API文档要求，每次申请后等待4秒
		time.Sleep(4 * time.Second)
	}

	return nil
}

func GetMerchantsEpc(token string, limit int, page int) (*partnermaticvo.GetMerchantsEpcResp, *ecode.ErrCode) {
	ctx := context.Background()

	params := map[string]interface{}{}
	headers := map[string]string{
		"accept":             "application/json, text/plain, */*",
		"accept-language":    "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
		"cache-control":      "no-cache",
		"content-type":       "application/json",
		"origin":             "https://app.partnermatic.com",
		"pragma":             "no-cache",
		"priority":           "u=1, i",
		"referer":            "https://app.partnermatic.com/",
		"sec-ch-ua":          `"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"`,
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": `"macOS"`,
		"sec-fetch-dest":     "empty",
		"sec-fetch-mode":     "cors",
		"sec-fetch-site":     "same-site",
		"user-agent":         "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
	}
	requestBody := map[string]interface{}{
		"appId": 32,
		"req": map[string]interface{}{
			"header": map[string]interface{}{
				"token": token,
			},
			"fields":     []interface{}{},
			"attributes": map[string]interface{}{},
			"filter": map[string]interface{}{
				"keyword":         "",
				"cat_ids":         "",
				"direct_country":  "",
				"verify_type":     "",
				"ad_type":         "",
				"order_type":      "epc",
				"order_direction": "desc",
			},
			"page": map[string]interface{}{
				"number": page,
				"size":   limit,
			},
		},
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	resp, err := remoteInvokeWithUrl(ctx, hostByApi+apiGetMerchantsEps, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}

	getMerchantsEpcResp := new(partnermaticvo.GetMerchantsEpcResp)
	err = json.Unmarshal(resp, getMerchantsEpcResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetMerchantsEpc json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getMerchantsEpcResp, nil
}

func BatchGetMerchantsEpc(limit int, accountName string, account string, password string) ([][]string, *ecode.ErrCode) {
	allMerchantsEpc := make([][]string, 0)
	uniqueMap := make(map[string]bool)
	page := 1
	limit = 100
	formattedDate := time.Now().Format("2006-01-02")
	// 获取 token
	getToken, err := GetToken(account, password)
	if err != nil {
		return allMerchantsEpc, ecode.With(ecode.ErrCodeHTTP, err)
	}
	for {
		// 调用 GetMerchants 函数
		getMerchantsEpcResp, err := GetMerchantsEpc(getToken.Data.AuthToken, limit, page)
		if err != nil {
			return allMerchantsEpc, err
		}

		// 将获取到的商家添加到总列表中
		for _, merchant := range getMerchantsEpcResp.Data.List {
			day30Epc := ""
			day30Epc = strings.ReplaceAll(merchant.Epc, "$", "")
			day30Epc = strings.ReplaceAll(day30Epc, ",", "")
			day30EpcFloat, errc := strconv.ParseFloat(day30Epc, 64)
			if errc != nil {
				// 处理错误
				fmt.Println("转换错误:", errc)
				continue
			}
			if day30EpcFloat < 5 {
				return allMerchantsEpc, nil
			}
			if day30EpcFloat < 5 {
				continue
			}
			uniqueKey := strconv.Itoa(merchant.Id) + merchant.Mcid
			if _, exists := uniqueMap[uniqueKey]; !exists {
				merchantRow := []string{
					strconv.Itoa(merchant.Id),
					merchant.Mcid,
					merchant.Name,
					merchant.CatName,
					merchant.Region,
					merchant.DirectCountry,
					domainutil.ExtractDomain(merchant.Website),
					formattedDate,
					accountName,
					day30Epc,
					strconv.Itoa(merchant.PaymentCycle),
					fmt.Sprintf("%v", merchant.Cvr),
					fmt.Sprintf("%v", merchant.Status),
					merchant.Commission,
				}
				allMerchantsEpc = append(allMerchantsEpc, merchantRow)
				uniqueMap[uniqueKey] = true
			}
		}

		// 如果当前页是最后一页，退出循环
		if page*limit >= getMerchantsEpcResp.Data.Total {
			break
		}

		// 更新页码，获取下一页
		page = page + 1
	}

	return allMerchantsEpc, nil
}
