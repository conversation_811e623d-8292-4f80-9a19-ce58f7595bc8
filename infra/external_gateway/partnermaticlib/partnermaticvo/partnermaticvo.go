package partnermaticvo

type GetMerchantsEpcResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		List []struct {
			Id             int    `json:"id"`
			Mcid           string `json:"mcid"`
			Name           string `json:"name"`
			Website        string `json:"website"`
			Status         int    `json:"status"`
			DirectCountry  string `json:"direct_country"`
			AdType         string `json:"ad_type"`
			Logo           string `json:"logo"`
			Commission     string `json:"commission"`
			Epc            string `json:"epc"`
			Cvr            string `json:"cvr"`
			CookieRd       string `json:"cookie_rd"`
			PaymentCycle   int    `json:"payment_cycle"`
			ActivationTime string `json:"activation_time"`
			DomainLimit    struct {
				Type    int         `json:"type"`
				Domains interface{} `json:"domains"`
			} `json:"domain_limit"`
			Acceptance       string `json:"acceptance"`
			Currency         string `json:"currency"`
			Region           string `json:"region"`
			AllowDeeplink    int    `json:"allow_deeplink"`
			CatId            int    `json:"cat_id"`
			CatName          string `json:"cat_name"`
			CurrentStatusCnt int    `json:"current_status_cnt"`
			AllStatusCnt     int    `json:"all_status_cnt"`
			RequestTime      string `json:"request_time"`
			ActiveTime       string `json:"active_time"`
			RejectTime       string `json:"reject_time"`
		} `json:"list"`
		Total int `json:"total"`
	} `json:"data"`
}

type GetOrderTransactionsResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Total     int         `json:"total"`
		CurPage   interface{} `json:"curPage"`
		TotalPage int         `json:"totalPage"`
		HasNext   bool        `json:"hasNext"`
		List      []struct {
			PartnerboostId  string  `json:"partnerboost_id"`
			Mcid            string  `json:"mcid"`
			MerchantName    string  `json:"merchant_name"`
			OrderId         string  `json:"order_id"`
			OrderTime       int     `json:"order_time"`
			SaleAmount      float64 `json:"sale_amount"`
			SaleComm        float64 `json:"sale_comm"`
			Status          string  `json:"status"`
			NormId          string  `json:"norm_id"`
			OriAmount       float64 `json:"ori_amount"`
			OriAffBrokerage float64 `json:"ori_aff_brokerage"`
			ProdId          string  `json:"prod_id"`
			OrderUnit       int     `json:"order_unit"`
			Uid             string  `json:"uid"`
			Uid2            string  `json:"uid2"`
			Uid3            string  `json:"uid3"`
			Uid4            string  `json:"uid4"`
			Uid5            string  `json:"uid5"`
			ClickRef        string  `json:"click_ref"`
			CommRate        string  `json:"comm_rate"`
			ValidationDate  string  `json:"validation_date"`
			Note            string  `json:"note"`
			CustomerCountry string  `json:"customer_country"`
			VoucherCode     string  `json:"voucher_code"`
			IsDirect        int     `json:"is_direct"`
			ChannelId       string  `json:"channel_id"`
			BrandId         string  `json:"brand_id"`
			LastUpdateTime  string  `json:"last_update_time"`
			PaidStatus      int     `json:"paid_status"`
		} `json:"list"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		TotalMcid int         `json:"total_mcid"`
		TotalPage int         `json:"total_page"`
		Limit     interface{} `json:"limit"`
		List      []struct {
			Mcid                string      `json:"mcid"`
			Mid                 int         `json:"mid"`
			BrandId             int         `json:"brand_id"`
			MerchantName        string      `json:"merchant_name"`
			CommRate            string      `json:"comm_rate"`
			CommDetail          string      `json:"comm_detail"`
			SiteUrl             string      `json:"site_url"`
			Logo                string      `json:"logo"`
			Categories          string      `json:"categories"`
			Tags                string      `json:"tags"`
			OfferType           string      `json:"offer_type"`
			NetworkPartner      string      `json:"network_partner"`
			AvgPaymentCycle     int         `json:"avg_payment_cycle"`
			AvgPayout           string      `json:"avg_payout"`
			Country             string      `json:"country"`
			SupportRegion       string      `json:"support_region"`
			BrandStatus         string      `json:"brand_status"`
			MerchantStatus      string      `json:"merchant_status"`
			Datetime            int         `json:"datetime"`
			Relationship        string      `json:"relationship"`
			TrackingUrl         string      `json:"tracking_url"`
			TrackingUrlShort    string      `json:"tracking_url_short"`
			RD                  string      `json:"RD"`
			SiteDesc            string      `json:"site_desc"`
			FilterWords         string      `json:"filter_words"`
			CurrencyName        string      `json:"currency_name"`
			AllowSml            string      `json:"allow_sml"`
			PostAreaList        []string    `json:"post_area_list"`
			RepName             string      `json:"rep_name"`
			RepEmail            string      `json:"rep_email"`
			MlinkHash           string      `json:"mlink_hash"`
			BrandType           string      `json:"brand_type"`
			IsDirect            int         `json:"is_direct"`
			SupportCouponordeal interface{} `json:"support_couponordeal"`
		} `json:"list"`
	} `json:"data"`
}

type GetTokenResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Uid        int    `json:"uid"`
		Uname      string `json:"uname"`
		SiteStatus string `json:"site_status"`
		ExpireTime string `json:"expire_time"`
		AuthToken  string `json:"auth_token"`
	} `json:"data"`
}
