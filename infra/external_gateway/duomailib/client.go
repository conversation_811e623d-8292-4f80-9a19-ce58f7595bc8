package duomailib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

type DuomaiClient struct {
	AppKey    string
	AppSecret string
	BaseURL   string
}

func NewDuomaiClient(appKey, appSecret string) *DuomaiClient {
	return &DuomaiClient{
		AppKey:    appKey,
		AppSecret: appSecret,
		BaseURL:   "https://open.duomai.com/apis",
	}
}

func (c *DuomaiClient) generateSign(params map[string]string, businessParams map[string]interface{}) string {
	// 1. 按键排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 拼接字符串
	signStr := c.AppSecret
	for _, key := range keys {
		signStr += key + params[key]
	}

	// 3. 加入业务参数的JSON字符串
	businessJSON, _ := json.Marshal(businessParams)
	signStr += string(businessJSON)
	signStr += c.AppSecret

	// 4. MD5加密并转大写
	hasher := md5.New()
	hasher.Write([]byte(signStr))
	sign := hex.EncodeToString(hasher.Sum(nil))
	sign = strings.ToUpper(sign)
	return sign
}

func (c *DuomaiClient) GetPromotionPlans(query *string, storeID *string, isApply *bool, page int, pageSize int) (map[string]interface{}, error) {
	// 准备请求参数
	params := url.Values{}
	params.Set("app_key", c.AppKey)
	params.Set("timestamp", strconv.FormatInt(time.Now().Unix(), 10))
	params.Set("service", "cps-mesh.open.stores.plans.get")

	// 业务参数
	businessParams := map[string]interface{}{
		"page":      strconv.Itoa(page),
		"page_size": strconv.Itoa(pageSize),
	}

	if query != nil {
		businessParams["query"] = *query
	}
	if storeID != nil {
		businessParams["store_id"] = *storeID
	}
	if isApply != nil {
		businessParams["is_apply"] = strconv.FormatBool(*isApply)
	}

	// 生成签名
	signParams := make(map[string]string)
	for k, v := range params {
		signParams[k] = v[0]
	}
	params.Set("sign", c.generateSign(signParams, businessParams))

	// 发送请求
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	resp, err := c.sendRequest("POST", c.BaseURL, params, businessParams, headers)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return result, nil
}

func (c *DuomaiClient) GetAllPromotionPlans(query *string, storeID *string, isApply *bool, pageSize int) ([]interface{}, error) {
	allPlans := make([]interface{}, 0)
	page := 1

	for {
		result, err := c.GetPromotionPlans(query, storeID, isApply, page, pageSize)
		if err != nil {
			break
		}

		if status, ok := result["status"].(float64); !ok || int(status) != 0 {
			break
		}

		data, ok := result["data"].([]interface{})
		if !ok {
			break
		}

		if len(data) == 0 {
			break
		}

		allPlans = append(allPlans, data...)

		if len(data) < pageSize {
			break
		}

		page++
		time.Sleep(1 * time.Second)
	}

	return allPlans, nil
}

func (c *DuomaiClient) GetOrders(startTime int64, endTime int64, siteID *string, adsID *string, euid *string, orderField string, status *int, page int, pageSize int) (map[string]interface{}, error) {
	// 准备请求参数
	params := url.Values{}
	params.Set("app_key", c.AppKey)
	params.Set("timestamp", strconv.FormatInt(time.Now().Unix(), 10))
	params.Set("service", "cps-mesh.open.orders.query.get")

	businessParams := map[string]interface{}{
		"stime":       startTime,
		"etime":       endTime,
		"page":        page,
		"page_size":   pageSize,
		"order_field": orderField,
	}

	if siteID != nil {
		businessParams["site_id"] = *siteID
	}
	if adsID != nil {
		businessParams["ads_id"] = *adsID
	}
	if euid != nil {
		businessParams["euid"] = *euid
	}
	if status != nil {
		businessParams["status"] = *status
	}

	// 生成签名
	signParams := make(map[string]string)
	for k, v := range params {
		signParams[k] = v[0]
	}
	params.Set("sign", c.generateSign(signParams, businessParams))
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	resp, err := c.sendRequest("POST", c.BaseURL, params, businessParams, headers)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	var result map[string]interface{}
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	return result, nil
}

func (c *DuomaiClient) GetAllOrders(startTime int64, endTime int64, options map[string]interface{}) ([]map[string]interface{}, error) {
	allOrders := make([]map[string]interface{}, 0)
	currentTime := startTime

	const ONE_DAY = 86400

	for currentTime < endTime {
		queryEndTime := currentTime + ONE_DAY
		if queryEndTime > endTime {
			queryEndTime = endTime
		}

		page := 1
		for {
			result, err := c.GetOrders(
				currentTime,
				queryEndTime,
				getOptionalString(options, "site_id"),
				getOptionalString(options, "ads_id"),
				getOptionalString(options, "euid"),
				getOptionalStringWithDefault(options, "order_field", "update_time"),
				getOptionalInt(options, "status"),
				page,
				getOptionalIntWithDefault(options, "page_size", 200),
			)
			if err != nil {
				break
			}

			if status, ok := result["status"].(float64); !ok || int(status) != 0 {
				break
			}

			orders, ok := result["data"].([]interface{})
			if !ok {
				break
			}

			if len(orders) == 0 {
				break
			}

			for _, order := range orders {
				if orderMap, ok := order.(map[string]interface{}); ok {
					if details, ok := orderMap["details"].([]interface{}); ok {
						for _, detail := range details {
							if detailMap, ok := detail.(map[string]interface{}); ok {
								// Create a new map to hold the merged data
								mergedData := make(map[string]interface{})
								// Copy the outer order data
								for key, value := range orderMap {
									if key != "details" { // Avoid copying the details array
										mergedData[key] = value
									}
								}
								// Copy the inner detail data
								for key, value := range detailMap {
									mergedData[key] = value
								}
								allOrders = append(allOrders, mergedData)
							}
						}
					}

				}
			}
			if len(orders) < getOptionalIntWithDefault(options, "page_size", 200) {
				break
			}

			page++
			time.Sleep(1 * time.Second) // 添加延时避免请求过于频繁
		}
		currentTime = queryEndTime
		time.Sleep(1 * time.Second) // 每天数据查询完后添加延时
	}
	return allOrders, nil
}

func (c *DuomaiClient) sendRequest(method string, urlStr string, params url.Values, businessParams map[string]interface{}, headers map[string]string) ([]byte, error) {

	var body io.Reader
	if method == "POST" {
		jsonBody, _ := json.Marshal(businessParams)
		body = strings.NewReader(string(jsonBody))
	}

	req, err := http.NewRequest(method, urlStr, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.URL.RawQuery = params.Encode()

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP error: %s, %d", resp.Status, resp.StatusCode)
	}

	return respBody, nil
}

func getOptionalString(options map[string]interface{}, key string) *string {
	if val, ok := options[key].(string); ok {
		return &val
	}
	return nil
}

func getOptionalStringWithDefault(options map[string]interface{}, key string, defaultValue string) string {
	if val, ok := options[key].(string); ok {
		return val
	}
	return defaultValue
}

func getOptionalInt(options map[string]interface{}, key string) *int {
	if val, ok := options[key].(float64); ok {
		intVal := int(val)
		return &intVal
	}
	return nil
}

func getOptionalIntWithDefault(options map[string]interface{}, key string, defaultValue int) int {
	if val, ok := options[key].(float64); ok {
		return int(val)
	}
	return defaultValue
}

func BatchGetOrderTransactions(accountName string, token string, appKey string, status string, page int, limit int, startDay int, endDay int, exchangeRatesUsdMap map[string]float64) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)
	now := time.Now()
	startTime := now.AddDate(0, 0, startDay).Unix()
	endTime := now.AddDate(0, 0, endDay).Unix()
	client := NewDuomaiClient(appKey, token)
	// 获取订单
	allOrders, err := client.GetAllOrders(
		startTime,
		endTime,
		map[string]interface{}{
			"page_size": limit,
		},
	)
	if err != nil {
		return allOrders, ecode.With(ecode.ErrCodeJson, err)
	}
	if len(allOrders) > 0 {
		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, allOrders, exchangeRatesUsdMap)...)
	}
	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders []map[string]interface{}, exchangeRatesUsdMap map[string]float64) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders {
		tagCode := order["euid"].(string)
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t, err := time.Parse("2006-01-02 15:04:05", order["order_time"].(string))
		if err != nil {
			fmt.Println("Error parsing order_time:", err)
		}
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		commissionUsd := float64(0)
		if order["currency"] == "USD" {
			commissionUsd = order["order_commission"].(float64)
		} else {
			commission, err := strconv.ParseFloat(order["order_commission"].(string), 64)
			if err != nil {
				continue
			}
			commissionUsd = commission * exchangeRatesUsdMap[order["currency"].(string)]
		}

		orderStatus := strings.ToLower(strings.TrimSpace(fmt.Sprintf("%v", order["order_status"])))
		if orderStatus == "" {
			orderStatus = "pending"
		}
		unifiedStatus, found := constant.GetUnifiedOrderStatus(orderStatus)
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: 平台=%s, 原始状态='%s' -> 使用原状态\n", "fatcoupon", orderStatus)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order["id"],
			"account":          accountName,
			"order_id":         order["order_sn"],
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order["ads_name"],
			"merchant_id":      order["ads_name"],
			"commission":       order["order_commission"],
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             "",
			"ip":               "",
			"referer_url":      "",
			"customer_country": "",
			"currency":         order["currency"],
			"commission_usd":   commissionUsd,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)

	}

	return result
}
