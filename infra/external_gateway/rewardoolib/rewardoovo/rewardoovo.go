package rewardoovo

type GetOrderTransactionsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalPage  int    `json:"total_page"`
		TotalTrans string `json:"total_trans"`
		TotalItems string `json:"total_items"`
		Limit      int    `json:"limit"`
		List       []struct {
			MerchantName    string `json:"merchant_name"`
			OrderId         string `json:"order_id"`
			OrderTime       string `json:"order_time"`
			SaleAmount      string `json:"sale_amount"`
			SaleComm        string `json:"sale_comm"`
			Status          string `json:"status"`
			Uid             string `json:"uid"`
			ProdId          string `json:"prod_id"`
			OrderUnit       string `json:"order_unit"`
			Mcid            string `json:"mcid"`
			RewardooId      string `json:"rewardoo_id"`
			Mid             string `json:"mid"`
			Uid2            string `json:"uid2"`
			ClickRef        string `json:"click_ref"`
			CommRate        string `json:"comm_rate"`
			ValidationDate  string `json:"validation_date"`
			Note            string `json:"note"`
			CustomerCountry string `json:"customer_country"`
			VoucherCode     string `json:"voucher_code"`
			SiteUrl         string `json:"site_url"`
		} `json:"list"`
	} `json:"data"`
}

type GetMerchantsResp struct {
	Status struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	} `json:"status"`
	Data struct {
		TotalMcid string `json:"total_mcid"`
		TotalPage int    `json:"total_page"`
		Limit     int    `json:"limit"`
		List      []struct {
			Mcid                string        `json:"mcid"`
			Mid                 string        `json:"mid"`
			MerchantName        string        `json:"merchant_name"`
			CommRate            string        `json:"comm_rate"`
			SiteUrl             string        `json:"site_url"`
			Logo                string        `json:"logo"`
			Categories          string        `json:"categories"`
			Tags                string        `json:"tags"`
			OfferType           string        `json:"offer_type"`
			SupportDeeplink     string        `json:"support_deeplink"`
			PrimaryRegion       *string       `json:"primary_region"`
			Country             *string       `json:"country"`
			SupportRegion       string        `json:"support_region"`
			MerchantStatus      string        `json:"merchant_status"`
			Datetime            string        `json:"datetime"`
			Relationship        string        `json:"relationship"`
			TrackingUrl         string        `json:"tracking_url"`
			TrackingUrlShort    string        `json:"tracking_url_short"`
			TrackingUrlSmart    string        `json:"tracking_url_smart"`
			PromotionalMethods  []interface{} `json:"promotional_methods"`
			MlinkHash           string        `json:"mlink_hash"`
			SupportCouponordeal string        `json:"support_couponordeal"`
		} `json:"list"`
	} `json:"data"`
}

type GetMerchantsEpcResp struct {
	Data struct {
		TotalSize   int `json:"total_size"`
		TotalPage   int `json:"total_page"`
		PageSize    int `json:"page_size"`
		PageNum     int `json:"page_num"`
		CurrentSize int `json:"current_size"`
		Data        []struct {
			Id               string   `json:"id"`
			Logo             string   `json:"logo"`
			SiteUrl          string   `json:"site_url"`
			Sitename         string   `json:"sitename"`
			MId              string   `json:"m_id"`
			CId              string   `json:"c_id"`
			Addr             string   `json:"addr"`
			Userid           string   `json:"userid"`
			UnionId          string   `json:"union_id"`
			BillingCycleUnit string   `json:"billing_cycle_unit"`
			PrimaryRegion    string   `json:"primary_region"`
			SpecialType      string   `json:"special_type"`
			PricingModel     string   `json:"pricing_model"`
			PerSale          string   `json:"per_sale"`
			DefaultSale      string   `json:"default_sale"`
			Day30Epc         string   `json:"day_30_epc"`
			Acceptance       string   `json:"acceptance"`
			CR               string   `json:"CR"`
			Country          string   `json:"country"`
			Category         string   `json:"category"`
			IsCampaign       int      `json:"is_campaign"`
			IsCouponTracking int      `json:"is_coupon_tracking"`
			IsFollow         int      `json:"is_follow"`
			SupportRegions   []string `json:"support_regions"`
			ProductNumbers   int      `json:"product_numbers"`
			SiteList         []struct {
				PropertyType string `json:"property_type"`
				CName        string `json:"c_name"`
				Id           string `json:"id"`
				MediumId     string `json:"medium_id"`
				Title        string `json:"title"`
				Host         string `json:"host"`
				ApplyStatus  string `json:"apply_status"`
				UpdatedTime  string `json:"updated_time"`
				Isdefault    string `json:"isdefault"`
				AdvId        string `json:"adv_id"`
			} `json:"site_list"`
			SiteApprovedList []struct {
				PropertyType string `json:"property_type"`
				CName        string `json:"c_name"`
				Id           string `json:"id"`
				MediumId     string `json:"medium_id"`
				Title        string `json:"title"`
				Host         string `json:"host"`
				ApplyStatus  string `json:"apply_status"`
				UpdatedTime  string `json:"updated_time"`
				Isdefault    string `json:"isdefault"`
				AdvId        string `json:"adv_id"`
			} `json:"site_approved_list"`
			SitePendingList        []interface{} `json:"site_pending_list"`
			SiteDeclinedList       []interface{} `json:"site_declined_list"`
			SiteNoRelationshipList []interface{} `json:"site_no_relationship_list"`
			SiteSuspendedList      []interface{} `json:"site_suspended_list"`
			IsApply                int           `json:"is_apply"`
			TrackingCode           struct {
				Current []interface{} `json:"current"`
				Expired []interface{} `json:"expired"`
			} `json:"tracking_code"`
			IsAmazon      bool `json:"is_amazon"`
			ShopBrandType int  `json:"shop_brand_type"`
		} `json:"data"`
	} `json:"data"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	V       string `json:"v"`
}
