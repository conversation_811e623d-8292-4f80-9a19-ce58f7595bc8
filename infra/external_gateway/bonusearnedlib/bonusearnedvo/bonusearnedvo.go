package bonusearnedvo

import "time"

type GetOrderTransactionsResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Total     int `json:"total"`
		Page      int `json:"page"`
		PageSize  int `json:"page_size"`
		OrderList []struct {
			ConversionId string      `json:"conversion_id"`
			UserInfo     interface{} `json:"user_info"`
			UserCode     string      `json:"user_code"`
			MerchantInfo struct {
				Id            int    `json:"id"`
				Name          string `json:"name"`
				UniqueName    string `json:"unique_name"`
				MerchantCode  string `json:"merchant_code"`
				Logo          string `json:"logo"`
				Website       string `json:"website"`
				TrackUrl      string `json:"track_url"`
				CashbackValue string `json:"cashback_value"`
				Description   string `json:"description"`
				Category      struct {
					Id   int    `json:"id"`
					Name string `json:"name"`
					Icon string `json:"icon"`
				} `json:"category"`
				Featured bool `json:"featured"`
				Country  struct {
					Id            int       `json:"id"`
					Name          string    `json:"name"`
					Code          string    `json:"code"`
					Flag          string    `json:"flag"`
					MerchantCount int       `json:"merchant_count"`
					Status        int       `json:"status"`
					CreatedAt     time.Time `json:"created_at"`
					UpdatedAt     time.Time `json:"updated_at"`
				} `json:"country"`
				SupportedCountries []string  `json:"supported_countries"`
				CreatedAt          time.Time `json:"created_at"`
				UpdatedAt          time.Time `json:"updated_at"`
			} `json:"merchant_info"`
			MerchantCode   string    `json:"merchant_code"`
			OrderId        string    `json:"order_id"`
			ClickId        string    `json:"click_id"`
			Sub1           string    `json:"sub_1"`
			OrderAmount    string    `json:"order_amount"`
			CashbackAmount string    `json:"cashback_amount"`
			Status         string    `json:"status"`
			OrderTime      time.Time `json:"order_time"`
			ApproveTime    time.Time `json:"approve_time"`
			CancelTime     time.Time `json:"cancel_time"`
			PaidTime       time.Time `json:"paid_time"`
			CancelReason   string    `json:"cancel_reason"`
		} `json:"order_list"`
	} `json:"data"`
}
