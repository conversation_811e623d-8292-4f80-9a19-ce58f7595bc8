package bonusearnedlib

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/infra/external_gateway/bonusearnedlib/bonusearnedvo"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func GetOrderTransactions(token string, userCode string, status string, page int, limit int) (*bonusearnedvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	params := map[string]interface{}{
		"page":      strconv.Itoa(page),
		"page_size": strconv.Itoa(limit),
	}
	headers := map[string]string{
		"Authorization": "Bearer " + token,
		"x-user-code":   userCode,
	}

	resp, err := remoteInvokeWithUrl(ctx, host+apiGetOrderTransactions, http.MethodGet, params, headers, nil)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(bonusearnedvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("partnerboostlib GetOrderTransactions json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, userCode string, status string, page int, limit int) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)

	for {
		// 调用 GetOrderTransactions 函数
		getOrderTransactionsResp, err := GetOrderTransactions(token, userCode, status, page, limit)
		if err != nil {
			zap.L().Error("partnerboostlib BatchGetOrderTransactions GetOrderTransactions failed", zap.Error(err))
			return allOrderList, err
		}

		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSlice(accountName, getOrderTransactionsResp)...)

		// 如果获取到的订单长度为0，表示没有更多订单
		if page*limit >= getOrderTransactionsResp.Data.Total {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	return allOrderList, nil
}

func convertOrdersToSlice(accountName string, orders *bonusearnedvo.GetOrderTransactionsResp) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(orders.Data.OrderList))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.OrderList {
		tagCode := order.Sub1
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		formattedDateTime := order.OrderTime.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := order.OrderTime.Format("2006-1-2")
		orderTimeHour := order.OrderTime.Hour()
		unifiedStatus, found := constant.GetUnifiedOrderStatus(strings.ToLower(strings.TrimSpace(order.Status)))
		if !found {
			// 仅打印未知状态警告
			fmt.Printf("⚠️  未知状态: account=%s, 原始状态='%s' -> 使用原状态\n", accountName, order.Status)
		}
		orderMap := map[string]interface{}{
			"conversion_id":    order.ConversionId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.MerchantInfo.UniqueName,
			"merchant_id":      order.MerchantInfo.Id,
			"commission":       order.CashbackAmount,
			"order_status":     unifiedStatus,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             "",
			"ip":               "",
			"referer_url":      order.ClickId,
			"customer_country": "",
			"currency":         "USD",
			"commission_usd":   order.CashbackAmount,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}
		result = append(result, orderMap)
	}

	return result
}
