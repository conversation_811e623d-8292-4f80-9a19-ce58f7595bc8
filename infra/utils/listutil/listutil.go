package listutil

import (
	"strings"
)

// RemoveDuplicateRows 从二维数组中移除重复行
// array: 二维数组数据
// uniqueCols: 用于标识唯一行的列索引。如果为空，则使用第一列作为唯一标识符
// separator: 用于将唯一列值连接成单个键的分隔符
func RemoveDuplicateRows(array [][]string, uniqueCols []int, separator string) [][]string {
	if len(uniqueCols) == 0 {
		uniqueCols = []int{0} // 默认使用第一列作为唯一标识符
	}
	if separator == "" {
		separator = "-"
	}

	uniqueMap := make(map[string]bool)
	result := make([][]string, 0)

	for _, row := range array {
		if len(row) == 0 {
			continue
		}

		// 构建唯一键
		var keyParts []string
		for _, col := range uniqueCols {
			if col < len(row) {
				keyParts = append(keyParts, row[col])
			}
		}
		key := strings.Join(keyParts, separator)

		// 如果键不存在，添加到结果中
		if !uniqueMap[key] {
			uniqueMap[key] = true
			result = append(result, row)
		}
	}

	return result
}

// CreateUniqueValueMap 创建一个唯一值映射，使用第一列作为键
func CreateUniqueValueMap(orderList [][]string) map[string][]string {
	uniqueValueMap := make(map[string][]string)
	for _, orderInfo := range orderList {
		if len(orderInfo) >= 2 {
			key := orderInfo[0]
			uniqueValueMap[key] = orderInfo
		}
	}
	return uniqueValueMap
}
