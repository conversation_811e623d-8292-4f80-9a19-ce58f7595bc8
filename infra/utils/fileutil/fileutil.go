package fileutil

import (
	"bufio"
	"fmt"
	"os"
	"strings"
)

// WriteRows 写入二维数据到文件
func WriteRows(filename string, rows [][]interface{}) error {
	file, err := os.OpenFile(filename, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("open file error: %w", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	for _, row := range rows {
		// 将每一行的数据转换为字符串切片
		strRow := make([]string, len(row))
		for i, v := range row {
			strRow[i] = fmt.Sprintf("%v", v)
		}

		// 使用逗号连接并写入一行
		line := strings.Join(strRow, ",") + "\n"
		if _, err := writer.WriteString(line); err != nil {
			return fmt.Errorf("write line error: %w", err)
		}
	}

	return nil
}
