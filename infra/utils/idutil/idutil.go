package idutil

import (
	"fmt"
	"time"
)

// GenerateUniqueID 生成唯一的递增ID
func GenerateUniqueID() string {
	// 使用纳秒时间戳
	timestamp := time.Now().UnixNano()

	// 使用时间戳作为种子生成随机数，避免导入rand包
	seed := timestamp

	// 简单的线性同余生成器
	a := int64(1664525)
	c := int64(1013904223)
	m := int64(1) << 32

	rand1 := (a*seed + c) % m
	rand2 := (a*rand1 + c) % m

	// 获取当前goroutine的内存地址作为额外熵源
	var dummy [1]byte
	goroutineID := fmt.Sprintf("%p", &dummy)

	// 组合多个熵源：时间戳 + 多个随机数 + goroutine地址哈希
	return fmt.Sprintf("%d_%016x%016x_%s", timestamp, rand1, rand2, goroutineID[2:])
}
