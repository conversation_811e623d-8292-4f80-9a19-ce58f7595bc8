package timeutil

import "time"

// IsSameDay 判断两个日期是否是同一天
func IsSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// IsTimeInRange 判断时间是否在指定的时间范围内
func IsTimeInRange(t, start, end time.Time) bool {
	return t.After(start) && t.Before(end)
}

// IsTimeInHourRange 只比较时间的小时部分，判断 checkTime 是否在 startTime 和 endTime 的小时范围内
func IsTimeInHourRange(checkTime, startTime, endTime time.Time) bool {
	checkHour := checkTime.Hour()
	startHour := startTime.Hour()
	endHour := endTime.Hour()

	// 如果 startHour 小于等于 endHour，检查 checkHour 是否在范围内
	if startHour <= endHour {
		return checkHour >= startHour && checkHour <= endHour
	}
	// 如果时间范围跨越午夜（例如从 23:00 到 02:00），需要特殊处理
	return checkHour >= startHour || checkHour <= endHour
}
