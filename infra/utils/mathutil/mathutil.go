package mathutil

import "fmt"

func InSliceStr(allSlice []string, findStr string) bool {
	for _, str := range allSlice {
		if str == findStr {
			return true
		}
	}
	return false
}

// ArrayStringGroupsOf 把数组分割成多个正整数大小的数组，如果不够分，则最后一个数组分到剩余的所有元素。
func ArrayStringGroupsOf(arr []string, num int) [][]string {
	max := len(arr)
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]string{arr}
	}
	//获取应该数组分割为多少份
	var quantity int
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]string, 0)
	//声明分割数组的截止下标
	var start, end, i int
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// Convert2DListToMap 将二维列表转换为 map，key 为指定列的组合，value 为整行数据
func Convert2DListToMap(data [][]interface{}, keyCols []int) map[string][]interface{} {
	// 创建一个 map，key 为指定列的组合，value 为整行数据
	dataMap := make(map[string][]interface{})

	for _, row := range data {
		if len(row) < len(keyCols) {
			continue // 确保当前行有足够的列
		}

		// 组合 key
		key := ""
		for _, colIndex := range keyCols {
			if colIndex < len(row) {
				key += fmt.Sprintf("%v", row[colIndex]) // 拼接指定列的值
			}
		}

		dataMap[key] = row // 整行数据作为 value
	}

	return dataMap
}

func Abs(a float64) float64 {
	if a < 0 {
		return -a
	}
	return a
}
