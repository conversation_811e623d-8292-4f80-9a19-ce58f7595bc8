package dao

import (
	"brand-bidding-service/domain/merchant/entity"
	"database/sql"
	"fmt"
	"strings"
)

type MerchantDAO struct {
	db *sql.DB
}

func NewMerchantDAO(db *sql.DB) *MerchantDAO {
	return &MerchantDAO{db: db}
}

// BeginTx 开始事务
func (d *MerchantDAO) BeginTx() (*sql.Tx, error) {
	return d.db.Begin()
}

// CreateTable 创建商家表
func (d *MerchantDAO) CreateTable() error {
	// 从entity获取约束配置
	constraints := entity.GetMerchantConstraints()

	// 构建唯一约束SQL
	uniqueConstraint := fmt.Sprintf("UNIQUE(%s)", strings.Join(constraints.UniqueFields, ", "))

	_, err := d.db.Exec(`
		CREATE TABLE IF NOT EXISTS merchants (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			merchant_id TEXT NOT NULL,
			merchant_slug TEXT NOT NULL,
			merchant_name TEXT,
			category_name TEXT,
			country TEXT,
			supported_countries TEXT DEFAULT '{}',
			website TEXT,
			original_domain TEXT,
			cashback_info TEXT DEFAULT '0.8',
			sub1 TEXT DEFAULT '{}',
			platform_type TEXT NOT NULL,
			accounts_info TEXT DEFAULT '[]',
			created_at TEXT,
			updated_at TEXT,
			` + uniqueConstraint + `
		)
	`)
	if err != nil {
		return fmt.Errorf("创建商家表失败: %w", err)
	}

	// 执行表结构迁移
	if err := d.migrateTable(); err != nil {
		return fmt.Errorf("商家表结构迁移失败: %w", err)
	}

	return nil
}

// migrateTable 执行表结构迁移
func (d *MerchantDAO) migrateTable() error {
	// 获取当前表结构
	columns, err := d.getTableColumns("merchants")
	if err != nil {
		return fmt.Errorf("获取商家表结构失败: %w", err)
	}

	// 定义期望的字段结构
	expectedColumns := map[string]string{
		"id":                  "INTEGER PRIMARY KEY AUTOINCREMENT",
		"merchant_id":         "TEXT NOT NULL",
		"merchant_slug":       "TEXT NOT NULL",
		"merchant_name":       "TEXT",
		"category_name":       "TEXT",
		"country":             "TEXT",
		"supported_countries": "TEXT DEFAULT '{}'",
		"website":             "TEXT",
		"original_domain":     "TEXT",
		"cashback_info":       "TEXT DEFAULT '0.8'",
		"sub1":                "TEXT DEFAULT '{}'",
		"platform_type":       "TEXT NOT NULL",
		"accounts_info":       "TEXT DEFAULT '[]'",
		"created_at":          "TEXT",
		"updated_at":          "TEXT",
	}

	// 检查并添加缺失的字段
	addedFieldsCount := 0
	for columnName, columnDef := range expectedColumns {
		if !columns[columnName] {
			fmt.Printf("➕ 发现缺失字段: %s (%s)\n", columnName, columnDef)
			defaultValue := d.getDefaultValue(columnName, columnDef)
			var alterSQL string

			if defaultValue != "" {
				cleanDef := strings.Replace(columnDef, " NOT NULL", "", 1)
				alterSQL = fmt.Sprintf("ALTER TABLE merchants ADD COLUMN %s %s DEFAULT %s", columnName, cleanDef, defaultValue)
			} else {
				alterSQL = fmt.Sprintf("ALTER TABLE merchants ADD COLUMN %s %s", columnName, columnDef)
			}

			_, err := d.db.Exec(alterSQL)
			if err != nil {
				fmt.Printf("❌ 添加字段 %s 失败: %v\n", columnName, err)
				return fmt.Errorf("添加字段 %s 失败: %w", columnName, err)
			}
			addedFieldsCount++
		}
	}

	// 创建必要的索引
	if err := d.createIndexes(); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	return nil
}

// getTableColumns 获取表的所有字段
func (d *MerchantDAO) getTableColumns(tableName string) (map[string]bool, error) {
	rows, err := d.db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns := make(map[string]bool)
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull int
		var defaultValue interface{}
		var pk int

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			return nil, err
		}
		columns[name] = true
	}

	return columns, nil
}

// getDefaultValue 获取字段的默认值
func (d *MerchantDAO) getDefaultValue(columnName, columnDef string) string {
	if !strings.Contains(columnDef, "NOT NULL") {
		return ""
	}

	// 根据字段类型和名称提供默认值
	switch {
	case strings.Contains(columnDef, "TEXT"):
		if strings.Contains(columnDef, "DEFAULT") {
			// 从定义中提取默认值
			parts := strings.Split(columnDef, "DEFAULT ")
			if len(parts) > 1 {
				return strings.Trim(parts[1], " ")
			}
		}
		return "''"
	case strings.Contains(columnDef, "INTEGER"):
		return "0"
	default:
		return "''"
	}
}

// createIndexes 创建必要的索引
func (d *MerchantDAO) createIndexes() error {
	constraints := entity.GetMerchantConstraints()

	var indexes []struct {
		name string
		sql  string
	}

	// 为每个索引字段创建单列索引
	for _, field := range constraints.IndexFields {
		indexes = append(indexes, struct {
			name string
			sql  string
		}{
			name: fmt.Sprintf("idx_merchants_%s", field),
			sql:  fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_merchants_%s ON merchants(%s)", field, field),
		})
	}

	// 添加复合唯一索引（用于去重）
	indexes = append(indexes, struct {
		name string
		sql  string
	}{
		name: "idx_merchants_unique_dedup",
		sql:  fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS idx_merchants_unique_dedup ON merchants(%s)", strings.Join(constraints.UniqueFields, ", ")),
	})

	for _, index := range indexes {
		_, err := d.db.Exec(index.sql)
		if err != nil {
			fmt.Printf("❌ 创建索引 %s 失败: %v\n", index.name, err)
			return fmt.Errorf("创建索引失败 [%s]: %w", index.name, err)
		}
	}
	return nil
}

// GetMerchants 获取商家列表
func (d *MerchantDAO) GetMerchants(conditions map[string]interface{}) ([]entity.Merchant, error) {
	query := `SELECT id, merchant_id, merchant_slug, merchant_name, category_name, country, 
		supported_countries, website, original_domain, cashback_info, sub1, platform_type, 
		accounts_info, created_at, updated_at FROM merchants WHERE 1=1`
	args := []interface{}{}

	if platformType, ok := conditions["platform_type"].(string); ok && platformType != "" {
		query += " AND platform_type = ?"
		args = append(args, platformType)
	}
	if merchantID, ok := conditions["merchant_id"].(string); ok && merchantID != "" {
		query += " AND merchant_id = ?"
		args = append(args, merchantID)
	}
	if merchantSlug, ok := conditions["merchant_slug"].(string); ok && merchantSlug != "" {
		query += " AND merchant_slug = ?"
		args = append(args, merchantSlug)
	}

	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var merchants []entity.Merchant
	for rows.Next() {
		var merchant entity.Merchant
		err := rows.Scan(
			&merchant.ID,
			&merchant.MerchantID,
			&merchant.MerchantSlug,
			&merchant.MerchantName,
			&merchant.CategoryName,
			&merchant.Country,
			&merchant.SupportedCountries,
			&merchant.Website,
			&merchant.OriginalDomain,
			&merchant.CashbackInfo,
			&merchant.Sub1,
			&merchant.PlatformType,
			&merchant.AccountsInfo,
			&merchant.CreatedAt,
			&merchant.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		merchants = append(merchants, merchant)
	}
	return merchants, nil
}

// BatchCreateAndUpdateMerchants 分别批量新建和更新商家
func (d *MerchantDAO) BatchCreateAndUpdateMerchants(createMerchants []entity.Merchant, updateMerchants []entity.Merchant) error {
	var errors []string

	// 分别处理创建和更新，使用独立事务
	insertCount, insertErrors := d.batchCreateMerchantsWithSeparateTransaction(createMerchants)
	updateCount, updateErrors := d.batchUpdateMerchantsWithSeparateTransaction(updateMerchants)

	// 合并错误信息
	errors = append(errors, insertErrors...)
	errors = append(errors, updateErrors...)

	fmt.Printf("✅ 商家数据库操作完成: 新增 %d 条, 更新 %d 条", insertCount, updateCount)
	if len(errors) > 0 {
		fmt.Printf(", 错误 %d 条", len(errors))
	}
	return nil
}

// batchCreateMerchantsWithSeparateTransaction 独立事务批量创建商家
func (d *MerchantDAO) batchCreateMerchantsWithSeparateTransaction(createMerchants []entity.Merchant) (int, []string) {
	if len(createMerchants) == 0 {
		return 0, nil
	}

	tx, err := d.BeginTx()
	if err != nil {
		return 0, []string{fmt.Sprintf("创建商家事务开始失败: %v", err)}
	}
	defer tx.Rollback()

	insertStmt, err := tx.Prepare(`
		INSERT INTO merchants (
			id, merchant_id, merchant_slug, merchant_name, category_name, country,
			supported_countries, website, original_domain, cashback_info, sub1,
			platform_type, accounts_info, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return 0, []string{fmt.Sprintf("准备插入SQL失败: %v", err)}
	}
	defer insertStmt.Close()

	insertCount := 0
	var errors []string

	for _, merchant := range createMerchants {
		_, err := insertStmt.Exec(
			merchant.ID, merchant.MerchantID, merchant.MerchantSlug, merchant.MerchantName,
			merchant.CategoryName, merchant.Country, merchant.SupportedCountries,
			merchant.Website, merchant.OriginalDomain, merchant.CashbackInfo, merchant.Sub1,
			merchant.PlatformType, merchant.AccountsInfo, merchant.CreatedAt.Format("2006-01-02 15:04:05"),
			merchant.UpdatedAt.Format("2006-01-02 15:04:05"),
		)
		if err == nil {
			insertCount++
		} else {
			errors = append(errors, fmt.Sprintf("插入商家失败[%s]: %v", merchant.MerchantID, err))
		}
	}

	if err := tx.Commit(); err != nil {
		return 0, []string{fmt.Sprintf("创建商家事务提交失败: %v", err)}
	}

	return insertCount, errors
}

// batchUpdateMerchantsWithSeparateTransaction 独立事务批量更新商家
func (d *MerchantDAO) batchUpdateMerchantsWithSeparateTransaction(updateMerchants []entity.Merchant) (int, []string) {
	if len(updateMerchants) == 0 {
		return 0, nil
	}

	tx, err := d.BeginTx()
	if err != nil {
		return 0, []string{fmt.Sprintf("更新商家事务开始失败: %v", err)}
	}
	defer tx.Rollback()

	updateStmt, err := tx.Prepare(`
		UPDATE merchants SET 
			merchant_name = ?, category_name = ?, country = ?, supported_countries = ?,
			website = ?, original_domain = ?, cashback_info = ?, sub1 = ?,
			accounts_info = ?, updated_at = ?
		WHERE merchant_id = ? AND merchant_slug = ? AND platform_type = ?
	`)
	if err != nil {
		return 0, []string{fmt.Sprintf("准备更新SQL失败: %v", err)}
	}
	defer updateStmt.Close()

	updateCount := 0
	var errors []string

	for _, merchant := range updateMerchants {
		result, err := updateStmt.Exec(
			merchant.MerchantName, merchant.CategoryName, merchant.Country, merchant.SupportedCountries,
			merchant.Website, merchant.OriginalDomain, merchant.CashbackInfo, merchant.Sub1,
			merchant.AccountsInfo, merchant.UpdatedAt.Format("2006-01-02 15:04:05"),
			merchant.MerchantID, merchant.MerchantSlug, merchant.PlatformType,
		)
		if err != nil {
			errors = append(errors, fmt.Sprintf("更新商家失败[%s]: %v", merchant.MerchantID, err))
			continue
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil || rowsAffected == 0 {
			errors = append(errors, fmt.Sprintf("商家更新无影响行[%s]: 预期存在但未找到", merchant.MerchantID))
		} else {
			updateCount++
		}
	}

	if err := tx.Commit(); err != nil {
		return 0, []string{fmt.Sprintf("更新商家事务提交失败: %v", err)}
	}

	return updateCount, errors
}

// safeStringValue 安全地获取字符串值
func (d *MerchantDAO) safeStringValue(value interface{}) string {
	if value == nil {
		return ""
	}
	if str, ok := value.(string); ok {
		return str
	}
	return fmt.Sprintf("%v", value)
}
