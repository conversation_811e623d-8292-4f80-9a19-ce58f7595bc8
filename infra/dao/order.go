package dao

import (
	"brand-bidding-service/domain/order/entity"
	"database/sql"
	"fmt"
	"strings"
	"time"
)

type OrderDAO struct {
	db *sql.DB
}

func NewOrderDAO(db *sql.DB) *OrderDAO {
	return &OrderDAO{db: db}
}

// BeginTx 开始事务
func (d *OrderDAO) BeginTx() (*sql.Tx, error) {
	return d.db.Begin()
}

// CreateTable 创建订单表
func (d *OrderDAO) CreateTable() error {
	// 从entity获取约束配置
	constraints := entity.GetOrderConstraints()

	// 构建唯一约束SQL
	uniqueConstraint := fmt.Sprintf("UNIQUE(%s)", strings.Join(constraints.UniqueFields, ", "))

	_, err := d.db.Exec(`
		CREATE TABLE IF NOT EXISTS orders (
			id TEXT PRIMARY KEY,
			user_name TEXT,
			account TEXT,
			order_id TEXT,
			order_time_sec TEXT,
			order_time_day TEXT,
			merchant_name TEXT,
			commission REAL,
			order_status TEXT,
			tag_1 TEXT,
			tag_2 TEXT,
			ip TEXT,
			referer_url TEXT,
			customer_country TEXT,
			conversion_id TEXT,
			platform_type TEXT,
			merchant_id TEXT,
			order_amount REAL,
			order_amount_usd REAL,
			currency TEXT,
			commission_usd REAL,
			created_at TEXT,
			updated_at TEXT,
			` + uniqueConstraint + `
		)
	`)
	if err != nil {
		return fmt.Errorf("创建订单表失败: %w", err)
	}

	// 执行表结构迁移，确保所有必要的字段都存在
	if err := d.migrateTable(); err != nil {
		return fmt.Errorf("表结构迁移失败: %w", err)
	}

	return nil
}

// migrateTable 执行表结构迁移，类似 GORM 的 AutoMigrate
func (d *OrderDAO) migrateTable() error {
	// 获取当前表结构
	columns, err := d.getTableColumns()
	if err != nil {
		fmt.Printf("❌ 获取表结构失败: %v\n", err)
		return fmt.Errorf("获取表结构失败: %w", err)
	}
	// 定义期望的字段结构
	expectedColumns := map[string]string{
		"id":               "TEXT PRIMARY KEY",
		"user_name":        "TEXT",
		"account":          "TEXT",
		"order_id":         "TEXT",
		"order_time_sec":   "TEXT",
		"order_time_day":   "TEXT",
		"merchant_name":    "TEXT",
		"commission":       "REAL",
		"order_status":     "TEXT",
		"tag_1":            "TEXT",
		"tag_2":            "TEXT",
		"ip":               "TEXT",
		"referer_url":      "TEXT",
		"customer_country": "TEXT",
		"conversion_id":    "TEXT",
		"platform_type":    "TEXT",
		"merchant_id":      "TEXT",
		"order_amount":     "REAL",
		"order_amount_usd": "REAL",
		"currency":         "TEXT",
		"commission_usd":   "REAL",
		"created_at":       "TEXT",
		"updated_at":       "TEXT",
	}

	// 检查并添加缺失的字段
	addedFieldsCount := 0
	for columnName, columnDef := range expectedColumns {
		if !columns[columnName] {
			fmt.Printf("➕ 发现缺失字段: %s (%s)\n", columnName, columnDef)
			// SQLite 不支持修改列定义，只能添加新列
			// 对于 NOT NULL 字段，需要提供默认值
			defaultValue := d.getDefaultValue(columnName, columnDef)
			var alterSQL string

			if defaultValue != "" {
				// 有默认值的情况，先去掉 NOT NULL 约束添加字段，再处理约束
				cleanDef := strings.Replace(columnDef, " NOT NULL", "", 1)
				alterSQL = fmt.Sprintf("ALTER TABLE orders ADD COLUMN %s %s DEFAULT %s", columnName, cleanDef, defaultValue)
			} else {
				alterSQL = fmt.Sprintf("ALTER TABLE orders ADD COLUMN %s %s", columnName, columnDef)
			}

			_, err := d.db.Exec(alterSQL)
			if err != nil {
				fmt.Printf("❌ 添加字段 %s 失败: %v\n", columnName, err)
				return fmt.Errorf("添加字段 %s 失败: %w", columnName, err)
			}
			addedFieldsCount++
		}
	}

	// 创建必要的索引
	if err := d.createIndexes(); err != nil {
		fmt.Printf("❌ 创建索引失败: %v\n", err)
		return fmt.Errorf("创建索引失败: %w", err)
	}
	return nil
}

// getTableColumns 获取表的所有字段
func (d *OrderDAO) getTableColumns() (map[string]bool, error) {
	rows, err := d.db.Query("PRAGMA table_info(orders)")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns := make(map[string]bool)
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull int
		var defaultValue interface{}
		var pk int

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			return nil, err
		}
		columns[name] = true
	}

	return columns, nil
}

// getDefaultValue 获取字段的默认值
func (d *OrderDAO) getDefaultValue(columnName, columnDef string) string {
	if !strings.Contains(columnDef, "NOT NULL") {
		return ""
	}

	// 根据字段类型和名称提供默认值
	switch {
	case strings.Contains(columnDef, "TEXT"):
		return "''"
	case strings.Contains(columnDef, "REAL"):
		return "0.0"
	case strings.Contains(columnDef, "INTEGER"):
		return "0"
	default:
		return "''"
	}
}

// createIndexes 创建必要的索引以提高查询性能
func (d *OrderDAO) createIndexes() error {
	// 从entity获取索引配置
	constraints := entity.GetOrderConstraints()

	// 动态构建索引定义
	var indexes []struct {
		name string
		sql  string
	}

	// 为每个索引字段创建单列索引
	for _, field := range constraints.IndexFields {
		indexes = append(indexes, struct {
			name string
			sql  string
		}{
			name: fmt.Sprintf("idx_orders_%s", field),
			sql:  fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_orders_%s ON orders(%s)", field, field),
		})
	}

	// 添加复合唯一索引（用于去重）
	indexes = append(indexes, struct {
		name string
		sql  string
	}{
		name: "idx_orders_unique_dedup",
		sql:  fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS idx_orders_unique_dedup ON orders(%s)", strings.Join(constraints.UniqueFields, ", ")),
	})

	successCount := 0
	for _, index := range indexes {
		_, err := d.db.Exec(index.sql)
		if err != nil {
			fmt.Printf("❌ 创建索引 %s 失败: %v\n", index.name, err)
			return fmt.Errorf("创建索引失败 [%s]: %w", index.name, err)
		}
		successCount++
	}
	return nil
}

// ensureTableExists 轻量级检查表是否存在（不执行迁移）
func (d *OrderDAO) ensureTableExists() error {
	var tableName string
	err := d.db.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'").Scan(&tableName)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("订单表不存在，请确保已通过bootstrap初始化")
		}
		return fmt.Errorf("检查表存在性失败: %w", err)
	}
	return nil
}

// GetOrders 获取订单列表
func (d *OrderDAO) GetOrders(conditions map[string]interface{}) ([]entity.Order, error) {
	query := `SELECT id, user_name, account, order_id, order_time_sec, order_time_day, merchant_name, 
		commission, order_status, tag_1, tag_2, ip, referer_url, customer_country, 
		conversion_id, platform_type, merchant_id, order_amount, order_amount_usd, currency, commission_usd, created_at, updated_at
		FROM orders WHERE 1=1`
	args := []interface{}{}

	if startTime, ok := conditions["start_time"].(time.Time); ok {
		// 如果指定了开始时间，默认从前一天开始查询
		startTime = startTime.AddDate(0, 0, -1)
		query += " AND order_time_day >= ?"
		args = append(args, startTime.Format("2006-01-02"))
	}
	if endTime, ok := conditions["end_time"].(time.Time); ok {
		query += " AND order_time_day <= ?"
		args = append(args, endTime.Format("2006-01-02"))
	}
	if userName, ok := conditions["user_name"].(string); ok && userName != "" {
		query += " AND user_name = ?"
		args = append(args, userName)
	}
	if account, ok := conditions["account"].(string); ok && account != "" {
		query += " AND account = ?"
		args = append(args, account)
	}
	if merchantName, ok := conditions["merchant_name"].(string); ok && merchantName != "" {
		query += " AND merchant_name = ?"
		args = append(args, merchantName)
	}
	if orderStatus, ok := conditions["order_status"].(string); ok && orderStatus != "" {
		query += " AND order_status = ?"
		args = append(args, orderStatus)
	}
	if tag1, ok := conditions["tag_1"].(string); ok && tag1 != "" {
		query += " AND tag_1 = ?"
		args = append(args, tag1)
	}

	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var orders []entity.Order
	for rows.Next() {
		var order entity.Order
		err := rows.Scan(
			&order.ID,
			&order.UserName,
			&order.Account,
			&order.OrderID,
			&order.OrderTimeSec,
			&order.OrderTimeDay,
			&order.MerchantName,
			&order.Commission,
			&order.OrderStatus,
			&order.Tag1,
			&order.Tag2,
			&order.Ip,
			&order.RefererUrl,
			&order.CustomerCountry,
			&order.ConversionId,
			&order.PlatformType,
			&order.MerchantId,
			&order.OrderAmount,
			&order.OrderAmountUsd,
			&order.Currency,
			&order.CommissionUsd,
			&order.CreatedAt,
			&order.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		orders = append(orders, order)
	}
	return orders, nil
}

// GetTotalCommission 获取订单总佣金
func (d *OrderDAO) GetTotalCommission(conditions map[string]interface{}) (float64, error) {
	query := `SELECT COALESCE(SUM(commission_usd), 0) FROM orders WHERE 1=1`
	args := []interface{}{}

	if startTime, ok := conditions["start_time"].(time.Time); ok {
		// 如果指定了开始时间，默认从前一天开始查询
		startTime = startTime.AddDate(0, 0, -1)
		query += " AND order_time_day >= ?"
		args = append(args, startTime.Format("2006-01-02"))
	}
	if endTime, ok := conditions["end_time"].(time.Time); ok {
		query += " AND order_time_day <= ?"
		args = append(args, endTime.Format("2006-01-02"))
	}
	if account, ok := conditions["account"].(string); ok && account != "" {
		query += " AND account = ?"
		args = append(args, account)
	}
	if merchantName, ok := conditions["merchant_name"].(string); ok && merchantName != "" {
		query += " AND merchant_name = ?"
		args = append(args, merchantName)
	}
	if orderStatus, ok := conditions["order_status"].(string); ok && orderStatus != "" {
		query += " AND order_status = ?"
		args = append(args, orderStatus)
	}
	if tag1, ok := conditions["tag_1"].(string); ok && tag1 != "" {
		query += " AND tag_1 = ?"
		args = append(args, tag1)
	}

	var totalCommission float64
	err := d.db.QueryRow(query, args...).Scan(&totalCommission)
	if err != nil {
		return 0, err
	}
	return totalCommission, nil
}

// BatchCreateAndUpdateOrders 分别批量新建和更新订单（DDD架构正确版本）
func (d *OrderDAO) BatchCreateAndUpdateOrders(createOrders []entity.Order, updateOrders []entity.Order) error {
	var errors []string

	// 分别处理创建和更新，使用独立事务
	insertCount, insertErrors := d.batchCreateOrdersWithSeparateTransaction(createOrders)
	updateCount, updateErrors := d.batchUpdateOrdersWithSeparateTransaction(updateOrders)

	// 合并错误信息
	errors = append(errors, insertErrors...)
	errors = append(errors, updateErrors...)

	fmt.Printf("✅ 数据库操作完成: 新增 %d 条, 更新 %d 条", insertCount, updateCount)
	if len(errors) > 0 {
		fmt.Printf(", 错误 %d 条", len(errors))
	}
	fmt.Println()

	// 如果有错误，返回错误信息用于邮件通知
	if len(errors) > 0 {
		return fmt.Errorf("部分订单同步失败: %v", errors)
	}

	return nil
}

// batchCreateOrdersWithSeparateTransaction 独立事务批量创建订单
func (d *OrderDAO) batchCreateOrdersWithSeparateTransaction(createOrders []entity.Order) (int, []string) {
	if len(createOrders) == 0 {
		return 0, nil
	}

	tx, err := d.BeginTx()
	if err != nil {
		return 0, []string{fmt.Sprintf("创建订单事务开始失败: %v", err)}
	}
	defer tx.Rollback()

	insertStmt, err := tx.Prepare(`
		INSERT INTO orders (
			id, user_name, account, order_id, order_time_sec, order_time_day, 
			merchant_name, order_status, tag_1, tag_2, ip, referer_url, 
			customer_country, conversion_id, platform_type, merchant_id,
			order_amount, order_amount_usd, commission, currency, commission_usd, 
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return 0, []string{fmt.Sprintf("准备插入SQL失败: %v", err)}
	}
	defer insertStmt.Close()

	insertCount := 0
	var errors []string

	for _, order := range createOrders {
		_, err := insertStmt.Exec(
			order.ID, order.UserName, order.Account, order.OrderID,
			order.OrderTimeSec, order.OrderTimeDay, order.MerchantName,
			order.OrderStatus, order.Tag1, order.Tag2, order.Ip,
			order.RefererUrl, order.CustomerCountry, order.ConversionId,
			order.PlatformType, order.MerchantId, order.OrderAmount,
			order.OrderAmountUsd, order.Commission, order.Currency,
			order.CommissionUsd, order.CreatedAt, order.UpdatedAt,
		)
		if err == nil {
			insertCount++
		} else {
			errors = append(errors, fmt.Sprintf("插入订单失败[%s]: %v", order.ConversionId, err))
		}
	}

	if err := tx.Commit(); err != nil {
		return 0, []string{fmt.Sprintf("创建订单事务提交失败: %v", err)}
	}

	return insertCount, errors
}

// batchUpdateOrdersWithSeparateTransaction 独立事务批量更新订单
func (d *OrderDAO) batchUpdateOrdersWithSeparateTransaction(updateOrders []entity.Order) (int, []string) {
	if len(updateOrders) == 0 {
		return 0, nil
	}

	tx, err := d.BeginTx()
	if err != nil {
		return 0, []string{fmt.Sprintf("更新订单事务开始失败: %v", err)}
	}
	defer tx.Rollback()

	updateStmt, err := tx.Prepare(`
		UPDATE orders SET 
			order_status = ?, commission = ?, commission_usd = ?, updated_at = ?
		WHERE conversion_id = ? AND user_name = ? AND platform_type = ? AND account = ? AND merchant_id = ?
	`)
	if err != nil {
		return 0, []string{fmt.Sprintf("准备更新SQL失败: %v", err)}
	}
	defer updateStmt.Close()

	updateCount := 0
	var errors []string

	for _, order := range updateOrders {
		result, err := updateStmt.Exec(
			order.OrderStatus, order.Commission, order.CommissionUsd, order.UpdatedAt,
			order.ConversionId, order.UserName, order.PlatformType, order.Account, order.MerchantId,
		)
		if err != nil {
			errors = append(errors, fmt.Sprintf("更新订单失败[%s]: %v", order.ConversionId, err))
			continue
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil || rowsAffected == 0 {
			errors = append(errors, fmt.Sprintf("订单更新无影响行[%s]: 预期存在但未找到", order.ConversionId))
		} else {
			updateCount++
		}
	}

	if err := tx.Commit(); err != nil {
		return 0, []string{fmt.Sprintf("更新订单事务提交失败: %v", err)}
	}

	return updateCount, errors
}
