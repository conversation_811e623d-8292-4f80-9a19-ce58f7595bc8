package ecode

// CodeSuccess 成功返回
var CodeSuccess = New(0, "success")

// ecode 设计原则，code为5位数组成
// 第一位代表错误级别，1-系统错误，2-数据库错误，3-一般功能错误，4-调用外部api错误

var (
	ErrCodeHTTP          = New(10001, "http error")
	ErrCodeJson          = New(10002, "json error")
	ErrCodeOpenFile      = New(10003, "打开文件失败")
	ErrCodeInvalidParam  = New(10004, "请求参数错误")
	ErrCodeNeedLogin     = New(10005, "需要登录")
	ErrCodeInternalError = New(10006, "内部错误")
)

var (
	ErrDbGet    = New(20001, "获取db数据失败")
	ErrDbCreate = New(20002, "创建db数据失败")
	ErrDbUpdate = New(20003, "更新db数据失败")
	ErrDbDelete = New(20004, "删除db数据失败")
)
