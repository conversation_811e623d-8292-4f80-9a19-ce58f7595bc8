package ecode

import "fmt"

const (
	ErrCodeUnknown = -1
)

type ErrCode struct {
	code int    // 错误码
	msg  string // 错误信息-自定义
	err  error  // debug 错误信息-error信息
}

func (err *ErrCode) Error() string {
	str := fmt.Sprintf("%s：%s", err.msg, err.err)
	return str
}

func (err *ErrCode) Code() int {
	return err.code
}

func (err *ErrCode) String() string {
	return err.msg
}

func (err *ErrCode) Err() error {
	return err.err
}

func New(code int, msg string) *ErrCode {
	return &ErrCode{
		code: code,
		msg:  msg,
	}
}

func With(errcode *ErrCode, err error) *ErrCode {
	errcode.err = err
	return errcode
}

func ErrorToErrCode(err error) *ErrCode {
	if err == nil {
		return nil
	}

	errorCode, ok := err.(*ErrCode)
	if ok {
		return errorCode
	}

	errCode := New(ErrCodeUnknown, err.Error())
	errCode.err = err
	return errCode
}
