package constant

const (
	UserNameLiPeiGuan = "peiguan.li"
	UserNameLiPeiRen  = "peiren.li"
	UserNamePengYan   = "yan.peng"
	UserNameLingTing  = "ting.ling"
)

const (
	AccountTypeLinkHaiTao  = "linkhaitao"
	AccountTypePb          = "pb"
	AccountTypePm          = "pm"
	AccountTypeDuoMai      = "duomai"
	AccountTypeFatCoupon   = "fatcoupon"
	AccountTypeLinkBux     = "linkbux"
	AccountTypeBlueAff     = "blueaff"
	AccountTypeRebatesMe   = "rebatesme"
	AccountTypeBonusArrive = "bonusarrive"
	AccountTypeJoinGekko   = "joingekko"
	AccountTypeEClickLink  = "eclicklink"
	AccountTypeRewardool   = "rewardool"
	AccountTypeFatPartner  = "fatpartner"
	AccountTypeBonusEarned = "bonusearned"
	AccountTypeYieldkit    = "yieldkit"
)

var OrderAccountList = []map[string]interface{}{
	{
		"token":        "TQuTnwtodeRFoWvL",
		"type":         AccountTypeLinkHaiTao,
		"account_name": "linkhaitao01",
		"limit":        40000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "UnJGZ1k5aEdsUVgxaXVFZTpka250akMybHk5d29XU3NjTmRiQ3diQzF1dHVjVHBwWQ==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpggm",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "anc0eXgxS0ZhZTFNNzFEazpGTklPWVFXY3VybGFETnI2alplYUZNOTh4ZUZqYTRuNg==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpg88",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "c3FoMER2SkNld1JFcGlmRjpiUkFzazdQZzJzV2dsZUFuaWdsQ2VDakZaVXhXOGhGOQ==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpr163",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "ZExOOVhKMmhEMHE2Q3NmMzppSVNGYlFwRlF1WUVFYzFXakFJMjllbnFMcFhxV2Vodw==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerpyqq",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	{
		"token":        "MVNYQXdzcUZmTFBsSXZ3SjpicU9SbXJ3TUFkbDdRaXVFNVVITHRlT2hrcldSTTJSZQ==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpgqq",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "R0o4bVVKOTJWMzlGOXdyczp6SFFtRXBsMTZSWW9paDJBY01pb2VtYmc1MWlZZ3hZQw==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpgclaraclark643",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "QUM0YzRhOUFVSUVucTFMVDpzUkpDZkh1WkhnUjFQYWlpcjlkb001U3Z4cVNiaWJVNQ==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlpg163",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "ZmFtQkhhSXlsSmZkSzlNbzpTYkREemc2eEdtZEtVd0F0ZURaQ3hIU25pV3o5czN2cA==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerlprgm",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "dXNEVXNzYUhRUnV4RjlNdTpkdHUxRTJsdVMyc2o1YmR1Z09jWjE0RFM3eFRpYkJyZw==",
		"type":         AccountTypeFatPartner,
		"account_name": "fatpartnerpygm",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	{
		"token":        "WgukkMkvpfsbomPE",
		"type":         AccountTypeLinkHaiTao,
		"account_name": "linkhaitao02",
		"limit":        40000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "32TwgbB2AVwy8biH",
		"type":         AccountTypeLinkHaiTao,
		"account_name": "linkhaitaopygm",
		"limit":        40000,
		"user_name":    UserNamePengYan,
	},
	{
		"token":        "9chXPXMqP7ycxva1",
		"type":         AccountTypeLinkHaiTao,
		"account_name": "linkhaitaolpr163",
		"limit":        40000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "v4B7IGYtSLWVaX9B",
		"type":         AccountTypePm,
		"account_name": "pm01",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "YLQgGcvxkUZj2xk9",
		"type":         AccountTypePm,
		"account_name": "pm02",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},

	{
		"token":        "v4B7IGYtSLWVaX9B",
		"type":         AccountTypePb,
		"account_name": "pb01",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "YLQgGcvxkUZj2xk9",
		"type":         AccountTypePb,
		"account_name": "pb02",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},

	{
		"account_name": "fatcoupon01",
		"type":         AccountTypeFatCoupon,
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.o9_PlDqdurlh3VmExdx6pN8K4p5KM2ZyQbKG1Hiyklk",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "fatcoupon02",
		"type":         AccountTypeFatCoupon,
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.obDTZTC2fPpdWToOf3nJuZ2y3kbGWC0gMl1n6YBF87E",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "fatcoupon03",
		"type":         AccountTypeFatCoupon,
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.mooH41Te9FSR0Idqsgeagq2Ob8RLmJAWacTZ2JJrDoE",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "fatcoupon01",
		"type":         AccountTypeFatCoupon,
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.zzYZg4-uOTXboCWf2YFUv6KA_N7COqK7cxD32Bey5c8",
		"limit":        2000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"account_name": "linkbux01",
		"type":         AccountTypeLinkBux,
		"token":        "mj2A8CXcblQZozBC",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxconan123",
		"type":         AccountTypeLinkBux,
		"token":        "jaxDAU8AuTxAoVjH",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxjacqueline",
		"type":         AccountTypeLinkBux,
		"token":        "fhWMqbtrL4KDdkLI",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxgcb01",
		"type":         AccountTypeLinkBux,
		"token":        "uRyP8fGNiOHdZL0T",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxgcbAnnie",
		"type":         AccountTypeLinkBux,
		"token":        "EgtTSfSMDHQd1ahC",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxgcbclaraclark",
		"type":         AccountTypeLinkBux,
		"token":        "1ElJZKFrmFMa9gdx",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "linkbuxgcb01",
		"type":         AccountTypeLinkBux,
		"token":        "DukRehBY8Miut5cW",
		"limit":        2000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"account_name": "blueafflpg",
		"type":         AccountTypeBlueAff,
		"token_ef":     "xZ3mw2FESFeJ8rbtgzKGEg",                                                                    // ef 后台
		"token":        "tgw_l7_route=8d7348a14213c84face3dc871c981e01; ba_pub_id=lsewwlhx3fkrzqsmo5196kulidoucaj5", // portal 后台
		"api_key":      "309b642d4acda9749b07aab3bfd8b1fb7c444235",
		"limit":        500,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "blueaff163lpg",
		"type":         AccountTypeBlueAff,
		"token_ef":     "neUo5sgPTMudHPd9gqyVQ",                                                                     // ef 后台
		"token":        "tgw_l7_route=8d7348a14213c84face3dc871c981e01; ba_pub_id=pbsaeoxbkiydszwrpr6fpta6c92lxdry", // pub 后台
		"api_key":      "6e1c0c0def3e038060b4c295816028f61df6a905",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "blueaffclaraclark",
		"type":         AccountTypeBlueAff,
		"token_ef":     "oTqoWzStQySoGUs3mBaXug",                                                                    // ef 后台
		"token":        "tgw_l7_route=af018731077a1cb74e09d727e3e9567d; ba_pub_id=fnmjkqggr1clc9o7ak8wnzi5pg6h2evg", // pub 后台
		"api_key":      "f73f6ef776a0f89bfcc50b3ebc43f85968e8398d",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "bonusarrivelpg",
		"type":         AccountTypeBonusArrive,
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYyMCIsInVzZXJfaWQiOiI1ODciLCJuYW1lIjoiUHJlbWl1bSBCcmFuZCBSZXZpZXdzIn0=.mXnkw+4Zll+ezG3KazlUsQwYwb+hBcUSzDrli+hdtxw=",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"account_name": "blueafflpr",
		"type":         AccountTypeBlueAff,
		"token_ef":     "kNHi1cJsR40z0M8SYl3YQ",                                                                     // ef 后台
		"token":        "tgw_l7_route=af018731077a1cb74e09d727e3e9567d; ba_pub_id=wg3raorjyrbwip365tdx51zf09qjjtjt", // pub 后台
		"api_key":      "69146821a16eeeb071dbf3389eb842532ca497ce",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"account_name": "blueaffpy",
		"type":         AccountTypeBlueAff,
		"token_ef":     "LkHx7voAQtuDk46h8VJ9Zw",                                                                    // ef 后台
		"token":        "tgw_l7_route=af018731077a1cb74e09d727e3e9567d; ba_pub_id=ui042hk592944j3lpvld13czygjcm3og", // pub 后台
		"api_key":      "3e0f525b80e813a629e4b6a84d121514a56384b5",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	{
		"account_name": "eclicklinklpg",
		"type":         AccountTypeEClickLink,
		"token":        "zeD7DRftRLa4b3QVCefmA", // ef 后台
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "RAIcZU4qvKKgCAPQ",
		"type":         AccountTypePm,
		"account_name": "pm03",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "RAIcZU4qvKKgCAPQ",
		"type":         AccountTypePb,
		"account_name": "pb03",
		"limit":        2000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"appkey":       "440336",
		"token":        "b348255dd8b8d4ef61682b23c7a7d5cb",
		"type":         AccountTypeDuoMai,
		"account_name": "duomai6341",
		"limit":        100,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "721046daac7288216d78f02409ae44ed",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollpggm",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "fe99393aa2ef5722b5bb915143030de6",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollpg163",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "7b16a78bd780f4fad0161454c754195f",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollpg88",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "129bcdb2f7b2b5ce5b28ee4cf92282a6",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollpgqq",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "ba0dde8b2c41c8e885e37eb3bd5d5f96",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoolpygm",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	{
		"token":        "b7195fa82b485bd74da288bb3d8bcdef",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoolpyqq",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	{
		"token":        "0eb60a435365ed3db523c1b0b57f3703",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollpr163",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "6215dfdec672df08cc2ce053b41a5df7",
		"type":         AccountTypeRewardool,
		"account_name": "rewardoollprgm",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.hJaP-wx71GYI4XVLj_mjprX-QF3DtjiMRcLcWa6KBZ8",
		"user_code":    "XVTSNFC",
		"type":         AccountTypeBonusEarned,
		"account_name": "bonusearnedlpggm",
		"limit":        1000,
		"user_name":    UserNameLiPeiGuan,
	},
	{
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.YMbQNLyagEbMJaw4g5MpMYXVypRiYqysOa1BjGfD5lY",
		"user_code":    "2JEUZZD",
		"type":         AccountTypeBonusEarned,
		"account_name": "bonusearnedlprgm",
		"limit":        1000,
		"user_name":    UserNameLiPeiRen,
	},
	{
		"token":        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.3lgPiK7HxvudkR6R5TAtCsJf9HjSCYIBetX5uy-5lh0",
		"user_code":    "NPYPDP3",
		"type":         AccountTypeBonusEarned,
		"account_name": "bonusearnedpygm",
		"limit":        1000,
		"user_name":    UserNamePengYan,
	},
	//{
	//	"token":        "pBIpiLLXbE2Dxzol",
	//	"type":         AccountTypePb,
	//	"account_name": "pblt",
	//	"limit":        2000,
	//	"user_name":    UserNameLingTing,
	//},
}

var MerchantsAccountList = []map[string]interface{}{
	{
		"account_name":        "yieldkitbonusearned",
		"type":                AccountTypeYieldkit,
		"api_key":             "41df4b33f24bcdc84c1b320893cb4ae8",
		"api_secret":          "ec9979aac017a3ea3eea95c28734355b",
		"site_id":             "b528d10c745f4d90a9a76b5d2ea6522d",
		"limit":               1000,
		"spread_sheet_id":     "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant": "yieldkit offer info",
		"user_name":           UserNameLiPeiGuan,
	},
	{
		"type":                                 AccountTypePm,
		"account_name":                         "pm01",
		"token":                                "v4B7IGYtSLWVaX9B",
		"authorization":                        "7a17oDSlosjpfBrrNnIyqHGtjhADaGxiOYzqZXRNIAe2wlBjXyOLDQeRcjEf2wdbOAcnilJtQYaYVzG5cI4paxNGRLSpIvPsB_aGaEjo79k8vm_bHvSrIKjF8iuzciSilw9eqb7qYW",
		"cookie":                               "__stripe_mid=4df038b0-00d9-4937-85c2-b0b50df6269891aeff; _ga=GA1.1.**********.**********; partnerboost_2132_BRAND_=a5d8gVaee1P5uiH0HKq_b7zFbH8vjQ4vrY5oglzKOC21_biYPYGxi_aex9h3ykev04uHC8M93llQscZ8g_c_c; partnerboost_2132_BRAND_74006=9db0pN7LZuP_ar3YRpfkJ4r6q8IHsti_aTTAHHHu5_bnInJ_bJIHQGE_c; partnerboost_2132_BRAND_94171=d459RthDg6Cp9wNuL207mxGS3o9EkBvqWoN2aN_a6BLeLyDacfqs_c; partnerboost_2132_BRAND_103131=0779rIusFyINubQFYRvR_b4E_b7NqOY40PMHVjM9K_aLFB7P8C8vgg_c; partnerboost_2132_BRAND_86301=5c63xMGPZlkamLxePj6lJoxKVHePYZjqai6Grm3zNkK5bZQ9NnkH_aS9E0jBY_bdYN; partnerboost_2132_BRAND_71939=6f82pBS_bxtWhJKx3N2okZs3G26MgTkWICZKav_bfQCYjtkmBUW4VvvOa4inCRZrcJ; cnst=c1YYN; gdpraccess=1; partnerboost_2132_lang=en; _user_uuid_=b58929c8-35da-4c21-8338-360349be4cff; __utrace=1a9f12af8e60871da2e865b52f18e45d; _ga_9FXP0L42KH=GS1.1.1734172995.10.0.1734172995.0.0.0; partnerboost_2132_saltkey=x87mh92S; partnerboost_2132_auth=7a17oDSlosjpfBrrNnIyqHGtjhADaGxiOYzqZXRNIAe2wlBjXyOLDQeRcjEf2wdbOAcnilJtQYaYVzG5cI4paxNGRLSpIvPsB_aGaEjo79k8vm_bHvSrIKjF8iuzciSilw9eqb7qYW; __stripe_sid=0a387aea-89fe-486d-a28a-31acd0ec13235967c4",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "partnermatic offer info",
		"sheet_name_merchant_business_metrics": "partnermatic offer metrics",
		"site_ids":                             "223486",
		"uuids":                                "8015832",
		"account":                              "<EMAIL>",
		"password":                             "lpg55813%%",
	},
	{
		"type":                                 AccountTypePb,
		"account_name":                         "pb01",
		"token":                                "v4B7IGYtSLWVaX9B",
		"authorization":                        "b894AAmUO2JcxYGDnyDCR7gRy8OITvX5_b49x6mdEZWRcmMAynqNLi02UlzeIEpVlWJzZMnnWNpUCxJ9LWIHLQwpuFKmS7QtlBSNyxPEC2I9AtV9QBV40sPpjvVpraWqT8RPEZhub",
		"cookie":                               "__stripe_mid=4df038b0-00d9-4937-85c2-b0b50df6269891aeff; _ga=GA1.1.**********.**********; partnerboost_2132_BRAND_94171=d459RthDg6Cp9wNuL207mxGS3o9EkBvqWoN2aN_a6BLeLyDacfqs_c; partnerboost_2132_BRAND_103131=0779rIusFyINubQFYRvR_b4E_b7NqOY40PMHVjM9K_aLFB7P8C8vgg_c; cnst=c1YYN; gdpraccess=1; partnerboost_2132_BRAND_74006=dfefMVcWUiYa0JoR2FeM8tE99zg3r4PfeKG6QyMlKaps7lHxJy5bgKaXhd2AUnLg; partnerboost_2132_BRAND_128089=c0c5L85nLxiHtRr2f4DLXhzUB2wxujdg96omXYzw_a4kciOLjLe4_c; partnerboost_2132_BRAND_89430=e1f7JK8Eun4btrvLmpKzzgQtcnU5tjvd5q6iXl9HdkZ_bP7kkRpA_c; partnerboost_2132_BRAND_85616=ab2czPwtQi4mKZ3YgtCfTqu8ctXFOYjBS_axcv9621obyxZ4_akgU_c; partnerboost_2132_BRAND_85614=ecae3_asRePhUbOM2Qo5m6Gg2Z0NDs6ySMgMmB06moQWfF_amAFJc_c; partnerboost_2132_BRAND_87954=0f13WBh5WZ0M4YMbzt6YEWexcCl_ar_bWsjUJ_a_admqOw7iqldm3qg_c; partnerboost_2132_BRAND_133739=77153WOaiDN23E58s9zPBjgEiGHAgwR4yFmpeEdbJZqCsJFTkssf6Xr_bV1ifd1tETgN0b5wLsL9nEQ_c_c; partnerboost_2132_BRAND_103867=0a8fughIsAbr0qylsGQHz58Mergu22JRJ1MewG0mA2Voc33GobE_c; partnerboost_2132_BRAND_94384=a66fqDFvboDkvri3J_b7lxhy_adc_aMHUF8ZHzn0utvJ4E5zha2zac_c; _ga_9FXP0L42KH=GS2.1.s1749012298$o16$g0$t1749012298$j60$l0$h0; partnerboost_2132_BRAND_84831=b46e7IYMeNaWZbS4OydinbktPn5U1ecz2BkUHkmi11MvH2KnnJg_c; partnerboost_2132_BRAND_88633=37caVtf51HRX4Qutxp0b355sJSWelKNCqh2pjCspw5F_ac5Hv2Dc_c; partnerboost_2132_BRAND_98808=d0fbIsyKB59afDyud8jdUmZHcviKz1D9YOr_b_a797FMKYYOdeeow_c; partnerboost_2132_BRAND_99121=0d4excMVYR5xBgzwUhk_bxljNBwojeMqTuMrmXA4HetCP5N_awMRE_c; partnerboost_2132_BRAND_107127=eec8Dt1WUkkzP4GUlvcWPXWLf2NnMRFN7ZKYAEaBfuymW1GCZtFJdQ2w2CyHgVh_a_aa60T3jVyslygw_c_c; partnerboost_2132_BRAND_105504=25f8ZGjZV_aF3JnxGlOQxIY5SYdEiTeEbgPQ_a_bMShDCAoy7GV8E0_c; partnerboost_2132_BRAND_86301=6f10cnqUrSrkkCyc6fDDLQSZx1QLrFpSouTu_b8Cgky4W_a6ytFRsXc1d4VeDdIhoZkhc4NI7ewaQt1g_c_c; partnerboost_2132_BRAND_128110=f4b3dToKYm5_auGFykQT_bKlqXd4PIqGOb1GC_a8ZRH0SzsmipqOx4_c; partnerboost_2132_BRAND_87816=9a36W1Yo7WE2GdJT0_bthhgR6LbiqOmHd3vmSKDxy3QEm1f66jxU_c; partnerboost_2132_BRAND_86298=3e64W93ENY8EVf967AgtOkWoym5lPhCOwQjmrazuqS2idv7XAQY_c; partnerboost_2132_BRAND_88864=956eBNA646K0ksnBbeUToPBk7ZdJtHbVcCgpYdqFAP6vtc8DBOg_c; partnerboost_2132_BRAND_88863=f70bfLGgJqwzdFv4OZhS5J1vETdgIMM8ytJ9Sr87ty_bxSaNpjsQ_c; partnerboost_2132_BRAND_71939=c31cuBSkBdP0tWnY4bzdpNdmbZi5jH6wh6GT0ooB0jOUeQ_alHcVgWrgfiWnmTzpMdTA_bHGA7xrtYsQ_c_c; partnerboost_2132_saltkey=W2X7q8Ec; partnerboost_2132_BRAND_84801=6db6xiVGwSdU7CyzcJbMHszAgqG_a_a4wBR6A3S6sC3oaApxlxt7M_c; partnerboost_2132_BRAND_85011=e253vqVjWtYoh6e9GGFqFIdIrZH21nbQGzw7pAv6tXSa15oxIOvfKIALwYLeUkhB; partnerboost_2132_BRAND_53558=7852BvykUo2R9l501N5Jio60_arl9zGZ88Rfrl5XlsKJiewlpQrvceLlFvoIs6wV5OSghklogdZjXXA_c_c; partnerboost_2132_lang=en; partnerboost_2132_BRAND_88777=6f00ZFwU3fVSMFkecReGpLusIWXjWUK_bXPhpz_bMPp_bdxSRyD0t6tnCYuDV0zeQvqfaPwm9NTt8XQgA_c_c; _user_uuid_=49d3f502-52ea-43bd-ba4e-d30bcf36bec1; __utrace=cabdaf020c24175a5bbddfd346cdc807; __stripe_sid=1280d5aa-c19a-45c9-aaae-eb9222ac6c3dc3e6a3; partnerboost_2132_auth=b894AAmUO2JcxYGDnyDCR7gRy8OITvX5_b49x6mdEZWRcmMAynqNLi02UlzeIEpVlWJzZMnnWNpUCxJ9LWIHLQwpuFKmS7QtlBSNyxPEC2I9AtV9QBV40sPpjvVpraWqT8RPEZhub; _ga_FL2K5YMSGY=GS2.1.s1756732166$o109$g1$t1756733394$j60$l0$h0",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "partnerboost offer info",
		"sheet_name_merchant_business_metrics": "partnerboost offer metrics",
		"site_ids":                             "223486",
		"uuids":                                "8015832",
		"account":                              "<EMAIL>",
		"password":                             "lpg55813%%",
	},
	{
		"type":                                 AccountTypePm,
		"account_name":                         "pm02",
		"token":                                "YLQgGcvxkUZj2xk9",
		"authorization":                        "2f32kPUjCLrvQVUoG6pqN_bri9xJ3zYIKUwICyvbfbfMd7ZoIf00us3SaQ5UuBSxCAP7NSyLFgE6cNmCTp4n53_agdGgRvfmzR2_aQVmYAC9M_buzhWUDSI2x1PEMgKIiidqt4McPp8T",
		"cookie":                               "__stripe_mid=4df038b0-00d9-4937-85c2-b0b50df6269891aeff; _ga=GA1.1.**********.**********; partnerboost_2132_BRAND_=a5d8gVaee1P5uiH0HKq_b7zFbH8vjQ4vrY5oglzKOC21_biYPYGxi_aex9h3ykev04uHC8M93llQscZ8g_c_c; partnerboost_2132_BRAND_94171=d459RthDg6Cp9wNuL207mxGS3o9EkBvqWoN2aN_a6BLeLyDacfqs_c; partnerboost_2132_BRAND_103131=0779rIusFyINubQFYRvR_b4E_b7NqOY40PMHVjM9K_aLFB7P8C8vgg_c; partnerboost_2132_BRAND_86301=5c63xMGPZlkamLxePj6lJoxKVHePYZjqai6Grm3zNkK5bZQ9NnkH_aS9E0jBY_bdYN; partnerboost_2132_BRAND_71939=6f82pBS_bxtWhJKx3N2okZs3G26MgTkWICZKav_bfQCYjtkmBUW4VvvOa4inCRZrcJ; cnst=c1YYN; gdpraccess=1; partnerboost_2132_lang=en; _user_uuid_=b58929c8-35da-4c21-8338-360349be4cff; __utrace=1a9f12af8e60871da2e865b52f18e45d; _ga_9FXP0L42KH=GS1.1.1734172995.10.0.1734172995.0.0.0; partnerboost_2132_saltkey=x87mh92S; partnerboost_2132_BRAND_74006=dfefMVcWUiYa0JoR2FeM8tE99zg3r4PfeKG6QyMlKaps7lHxJy5bgKaXhd2AUnLg; partnerboost_2132_auth=2f32kPUjCLrvQVUoG6pqN_bri9xJ3zYIKUwICyvbfbfMd7ZoIf00us3SaQ5UuBSxCAP7NSyLFgE6cNmCTp4n53_agdGgRvfmzR2_aQVmYAC9M_buzhWUDSI2x1PEMgKIiidqt4McPp8T; __stripe_sid=********-9a47-462c-b630-67a7128c7bf7b2f316",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "partnermatic offer info",
		"sheet_name_merchant_business_metrics": "partnermatic offer metrics",
		"site_ids":                             "225775",
		"uuids":                                "8016309",
		"account":                              "<EMAIL>",
		"password":                             "lpg55813%%",
	},
	{
		"type":                                 AccountTypePm,
		"account_name":                         "pm03",
		"token":                                "RAIcZU4qvKKgCAPQ",
		"authorization":                        "926dPKP_aK1Kt92yZkvsaMau2kcL6JmZN6CR8_bekINA1VlP3p5S5ApXw5MD3_a87FzbizIRm0TXrkVdo1_bMmJFTJuNF8dK080pRoK8q4no9tjlLVpfWYJauK8UNLwxVNr57uqoR52g",
		"cookie":                               "__stripe_mid=4df038b0-00d9-4937-85c2-b0b50df6269891aeff; _ga=GA1.1.**********.**********; partnerboost_2132_BRAND_=a5d8gVaee1P5uiH0HKq_b7zFbH8vjQ4vrY5oglzKOC21_biYPYGxi_aex9h3ykev04uHC8M93llQscZ8g_c_c; partnerboost_2132_BRAND_94171=d459RthDg6Cp9wNuL207mxGS3o9EkBvqWoN2aN_a6BLeLyDacfqs_c; partnerboost_2132_BRAND_103131=0779rIusFyINubQFYRvR_b4E_b7NqOY40PMHVjM9K_aLFB7P8C8vgg_c; partnerboost_2132_BRAND_86301=5c63xMGPZlkamLxePj6lJoxKVHePYZjqai6Grm3zNkK5bZQ9NnkH_aS9E0jBY_bdYN; partnerboost_2132_BRAND_71939=6f82pBS_bxtWhJKx3N2okZs3G26MgTkWICZKav_bfQCYjtkmBUW4VvvOa4inCRZrcJ; cnst=c1YYN; gdpraccess=1; partnerboost_2132_lang=en; _user_uuid_=b58929c8-35da-4c21-8338-360349be4cff; __utrace=1a9f12af8e60871da2e865b52f18e45d; _ga_9FXP0L42KH=GS1.1.1734172995.10.0.1734172995.0.0.0; partnerboost_2132_saltkey=x87mh92S; partnerboost_2132_BRAND_74006=dfefMVcWUiYa0JoR2FeM8tE99zg3r4PfeKG6QyMlKaps7lHxJy5bgKaXhd2AUnLg; __stripe_sid=863ce8ef-ed6b-4f86-9420-d949cb9c0b5388ea92; partnerboost_2132_auth=926dPKP_aK1Kt92yZkvsaMau2kcL6JmZN6CR8_bekINA1VlP3p5S5ApXw5MD3_a87FzbizIRm0TXrkVdo1_bMmJFTJuNF8dK080pRoK8q4no9tjlLVpfWYJauK8UNLwxVNr57uqoR52g",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "partnermatic offer info",
		"sheet_name_merchant_business_metrics": "partnermatic offer metrics",
		"site_ids":                             "281332",
		"uuids":                                "8021075",
		"account":                              "<EMAIL>",
		"password":                             "lpg55813%%",
	},
	//{
	//	"publisherKey":                         "2464bbaa-c87b-4487-9383-bc5ad17d103f",
	//	"propertyID":                           "1001039",
	//	"authKey":                              "y6plgxtvbskt6kvk3eoqqeat7a",
	//	"secretKey":                            "wapuzhrbbgkvgo4spq7plfkdvm",
	//	"type":                                 AccountTypeJoinGekko,
	//	"account_name":                         "joingekkolpg",
	//	"limit":                                100,
	//	"user_name":                            UserNameLiPeiGuan,
	//	"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
	//	"sheet_name_merchant":                  "joingekko offer info",
	//	"sheet_name_merchant_business_metrics": "",
	//},
	{
		"type":                                 AccountTypeLinkHaiTao,
		"account_name":                         "linkhaitao02",
		"token":                                "WgukkMkvpfsbomPE",
		"cookie":                               "cnst=c1YYN; HMACCOUNT=7DDF6A0EA5DD6344; _user_uuid_=5e5ee5fe-515f-43a2-9141-8628070ea804; __utrace=d9675fa7a5ffbd64dadb693f1391c93d; _ref=CR; lkht_98d6_saltkey=hg0zQZnx; gdpraccess=1; lkht_98d6_lang=cn; Hm_lvt_53760f061b903b8e61845f4fdaaae91d=**********; auth_token=U-********.95a9Z0KWd8V6vNPFGRk5jKEkd5Q_b5hP7s_arENaNKLFID622yVZseTj_bFjEqfqNm1FsMmvIB1Hc8q_buIkSyiMbBDzNkRyUU5qIuwC6vIgaX4RA1ZyI0zoRe5nuVOrOdb4GF7sYEfusA8_asQa3tR50zF7qPQ5qS9zs; _ucid=90757; Hm_lpvt_53760f061b903b8e61845f4fdaaae91d=**********; _gid=GA1.2.**********.**********; _gat_gtag_UA_206883571_1=1; _ga_N6QD2QCJZV=GS2.1.s**********$o807$g1$t1750064461$j54$l0$h0; _ga=GA1.1.*********.**********",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                2000,
		"limit_html":                           50,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkhaitao offer info",
		"sheet_name_merchant_business_metrics": "linkhaitao offer metrics",
		"site_id":                              11306,
	},
	{
		"type":                                 AccountTypeLinkHaiTao,
		"account_name":                         "linkhaitao01",
		"token":                                "TQuTnwtodeRFoWvL",
		"cookie":                               "cnst=c1YYN; gdpraccess=1; HMACCOUNT=0ED20605CF128D1B; _user_uuid_=bd6048f6-05ff-4689-ac6f-20b9c5b3160e; __utrace=e5ba099a61f3293c21154523c0490bec; Hm_lvt_53760f061b903b8e61845f4fdaaae91d=**********; auth_token=U-********.4797nlZTx4PjD4HioZu2gMx7JcYZvDmWaB1Fc6hZSkpm6CtEHmAd9efR8WRx8kpmEly2L3H0UCf2KMFvdrTQ6aSd_bqMhMHcdnbNEqpQw8uMrJi4AIL0H4xSnglt7QsnlC7Aeo9g8ZNm79H_albRzzeEc_b0IESQElP; _ucid=80416; lkht_98d6_saltkey=saQILkEv; _gid=GA1.2.*********.**********; userCollapse=false; Hm_lpvt_53760f061b903b8e61845f4fdaaae91d=**********; _ga_N6QD2QCJZV=GS2.1.s1755238186$o853$g1$t1755239206$j13$l0$h0; _ga=GA1.1.*********.**********",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                2000,
		"limit_html":                           50,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkhaitao offer info",
		"sheet_name_merchant_business_metrics": "linkhaitao offer metrics",
		"site_id":                              9748,
	},
	{
		"type":                                 AccountTypeLinkHaiTao,
		"account_name":                         "linkhaitaolpr163",
		"token":                                "9chXPXMqP7ycxva1",
		"cookie":                               "_user_uuid_=8942dabb-310e-4e73-b968-58086f622b78; __utrace=2d77bc8f9385f973e67376cd69d2042c; auth_token=U-********.179dRKpkshyGyAoYcNqtw0r5NwllFMpD8QDjPpdGBVm9FopnrLa_a_aT6wL6lgNCINF9MmvZWmvJMH8t_a0RrYYbQmWNb_a_a9mgpZJcXff5_byTLuuaePh2TAsonHfL6sKmmrFiDs9xHxzVhHRnkzbOHLVcxVzCVm7Pkoog_c_c; _ucid=130938; lkht_98d6_saltkey=rO9sDfIf; Hm_lvt_53760f061b903b8e61845f4fdaaae91d=**********; Hm_lpvt_53760f061b903b8e61845f4fdaaae91d=**********; HMACCOUNT=79367F89C96293BC; _gid=GA1.2.**********.**********; _ga_N6QD2QCJZV=GS1.1.**********.1.1.**********.0.0.0; _ga=GA1.1.**********.**********; cnst=c1YYN",
		"user_name":                            UserNameLiPeiRen,
		"limit":                                2000,
		"limit_html":                           50,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkhaitao offer info",
		"sheet_name_merchant_business_metrics": "linkhaitao offer metrics",
		"site_id":                              13929,
	},
	{
		"type":                                 AccountTypeLinkHaiTao,
		"account_name":                         "linkhaitaopygm",
		"token":                                "32TwgbB2AVwy8biH",
		"cookie":                               "lkht_98d6_saltkey=CQbFj9JI; _user_uuid_=3506afc2-ea8c-4fa0-a51d-17309cac3cf1; __utrace=f0b06a3d8a7b0445d5ecd63f9c8849d7; auth_token=U-********.3788QTu9lWzSDCLq9JgVIGJUvudOIMVeDthI3lRzyyOspSp6iz2Md_aMHvayUgNFT2TmdqD5WnHfR_anIsOKgz6I7yJAyMjiamdmKfU10pJizFRaSNNa2bkASxtCkLFIjrqHqW9Y5n6mxu9em8KYlvqI0J3X0_b903k4g_c_c; _ucid=130939; Hm_lvt_53760f061b903b8e61845f4fdaaae91d=**********; Hm_lpvt_53760f061b903b8e61845f4fdaaae91d=**********; HMACCOUNT=929A9D0EE93FEA9F; _gid=GA1.2.*********.**********; _ga_N6QD2QCJZV=GS1.1.**********.1.1.**********.0.0.0; _ga=GA1.1.*********.**********; cnst=c1YYN; gdpraccess=1",
		"user_name":                            UserNamePengYan,
		"limit":                                2000,
		"limit_html":                           50,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkhaitao offer info",
		"sheet_name_merchant_business_metrics": "linkhaitao offer metrics",
		"site_id":                              13930,
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbuxgcb01",
		"token":                                "uRyP8fGNiOHdZL0T",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbuxgcb offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbuxgcbAnnie",
		"token":                                "EgtTSfSMDHQd1ahC",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbuxgcb offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbuxgcbclaraclark",
		"token":                                "1ElJZKFrmFMa9gdx",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbuxgcb offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbux01",
		"token":                                "mj2A8CXcblQZozBC",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbux offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbuxconan123",
		"token":                                "jaxDAU8AuTxAoVjH",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbux offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeLinkBux,
		"account_name":                         "linkbuxjacqueline",
		"token":                                "fhWMqbtrL4KDdkLI",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "linkbux offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoollpggm",
		"token":                                "721046daac7288216d78f02409ae44ed",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoollpg163",
		"token":                                "fe99393aa2ef5722b5bb915143030de6",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoolpygm",
		"token":                                "ba0dde8b2c41c8e885e37eb3bd5d5f96",
		"user_name":                            UserNamePengYan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoolpyqq",
		"token":                                "b7195fa82b485bd74da288bb3d8bcdef",
		"user_name":                            UserNamePengYan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoollpr163",
		"token":                                "0eb60a435365ed3db523c1b0b57f3703",
		"user_name":                            UserNameLiPeiRen,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeRewardool,
		"account_name":                         "rewardoollprgm",
		"token":                                "6215dfdec672df08cc2ce053b41a5df7",
		"user_name":                            UserNameLiPeiRen,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "rewardool offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpggm",
		"token":                                "UnJGZ1k5aEdsUVgxaXVFZTpka250akMybHk5d29XU3NjTmRiQ3diQzF1dHVjVHBwWQ==",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpggm offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpg88",
		"token":                                "anc0eXgxS0ZhZTFNNzFEazpGTklPWVFXY3VybGFETnI2alplYUZNOTh4ZUZqYTRuNg==",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpg88 offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpr163",
		"token":                                "c3FoMER2SkNld1JFcGlmRjpiUkFzazdQZzJzV2dsZUFuaWdsQ2VDakZaVXhXOGhGOQ==",
		"user_name":                            UserNameLiPeiRen,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpr163 offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerpyqq",
		"token":                                "ZExOOVhKMmhEMHE2Q3NmMzppSVNGYlFwRlF1WUVFYzFXakFJMjllbnFMcFhxV2Vodw==",
		"user_name":                            UserNamePengYan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerpyqq offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpgqq",
		"token":                                "MVNYQXdzcUZmTFBsSXZ3SjpicU9SbXJ3TUFkbDdRaXVFNVVITHRlT2hrcldSTTJSZQ==",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpgqq offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpgclaraclark643",
		"token":                                "R0o4bVVKOTJWMzlGOXdyczp6SFFtRXBsMTZSWW9paDJBY01pb2VtYmc1MWlZZ3hZQw==",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpgclaraclark643 offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlpg163",
		"token":                                "QUM0YzRhOUFVSUVucTFMVDpzUkpDZkh1WkhnUjFQYWlpcjlkb001U3Z4cVNiaWJVNQ==",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlpg163 offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerlprgm",
		"token":                                "ZmFtQkhhSXlsSmZkSzlNbzpTYkREemc2eEdtZEtVd0F0ZURaQ3hIU25pV3o5czN2cA==",
		"user_name":                            UserNameLiPeiRen,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerlprgm offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeFatPartner,
		"account_name":                         "fatpartnerpygm",
		"token":                                "dXNEVXNzYUhRUnV4RjlNdTpkdHUxRTJsdVMyc2o1YmR1Z09jWjE0RFM3eFRpYkJyZw==",
		"user_name":                            UserNamePengYan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rTSFdaAndf2yiqHpqzZT4lA0D9K4NnuMuLE0O2S_leQ",
		"sheet_name_merchant":                  "fatpartnerpygm offer info",
		"sheet_name_merchant_business_metrics": "",
	},
	{
		"type":                                 AccountTypeBlueAff,
		"account_name":                         "blueafflpg",
		"token":                                "tgw_l7_route=8d7348a14213c84face3dc871c981e01; ba_pub_id=8dt4j5uwr2e46k8mtoyk74bra3p1rg0o",
		"api_key":                              "309b642d4acda9749b07aab3bfd8b1fb7c444235",
		"user_name":                            UserNameLiPeiGuan,
		"limit":                                1000,
		"spread_sheet_id":                      "1rMGhrHzqubefmBf1JSodacJMTcUXBiyB2B-HN14jA8E",
		"sheet_name_merchant":                  "blueaff offer info",
		"sheet_name_merchant_business_metrics": "",
	},
}
