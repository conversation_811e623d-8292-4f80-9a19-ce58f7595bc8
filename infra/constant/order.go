package constant

import "strings"

const (
	OrderStatusWithAll   = "all"
	OrderStatusWithFinal = "final_status" // 已经更新完状态的，包括已过期和已结算的

	// 统一的订单状态常量
	OrderStatusPending  = "pending"
	OrderStatusRejected = "rejected"
	OrderStatusApproved = "approved"
	OrderStatusPaid     = "paid"
)

// OrderStatusMapping 全局订单状态映射表
// 将各平台的原始状态映射到统一的4个状态
var OrderStatusMapping = map[string]string{
	// pending 状态
	"pending":   OrderStatusPending,
	"0":        OrderStatusPending,
	"untreated": OrderStatusPending,
	
	// rejected 状态
	"-20":      OrderStatusRejected,
	"expired":  OrderStatusRejected,
	"rejected": OrderStatusRejected,
	"declined": OrderStatusRejected,
	
	// approved 状态
	"approved":  OrderStatusApproved,
	"10":        OrderStatusApproved,
	"effective": OrderStatusApproved,
	
	// paid 状态
	"paid": OrderStatusPaid,
}

// GetUnifiedOrderStatus 获取统一的订单状态
// 如果找不到映射，返回原状态和false
func GetUnifiedOrderStatus(rawStatus string) (string, bool) {
	if rawStatus == "" {
		return OrderStatusPending, true // 空状态默认为pending
	}
	
	// 转换为小写进行匹配
	lowerStatus := strings.ToLower(strings.TrimSpace(rawStatus))
	
	if unifiedStatus, exists := OrderStatusMapping[lowerStatus]; exists {
		return unifiedStatus, true
	}
	
	// 未找到映射，返回原状态
	return rawStatus, false
}
