package constant

import "strings"

const (
	// 统一的商家状态常量
	MerchantStatusPending        = "pending"
	MerchantStatusRejected       = "rejected"
	MerchantStatusNoRelationship = "no relationship"
	MerchantStatusJoined         = "joined"
)

// MerchantStatusMapping 全局商家状态映射表
// 将各平台的原始状态映射到统一的4个状态
var MerchantStatusMapping = map[string]string{
	// pending 状态
	"pending": MerchantStatusPending,
	"n/a":     MerchantStatusPending,
	// rejected 状态
	"rejected": MerchantStatusRejected,
	// no relationship 状态
	"no relationship":  MerchantStatusNoRelationship,
	"require approval": MerchantStatusNoRelationship,
	// joined 状态
	"joined":    MerchantStatusJoined,
	"available": MerchantStatusJoined,
}

// GetUnifiedMerchantStatus 获取统一的订单状态
// 如果找不到映射，返回原状态和false
func GetUnifiedMerchantStatus(rawStatus string) (string, bool) {
	if rawStatus == "" {
		return MerchantStatusPending, true // 空状态默认为pending
	}

	// 转换为小写进行匹配
	lowerStatus := strings.ToLower(strings.TrimSpace(rawStatus))

	if unifiedStatus, exists := OrderStatusMapping[lowerStatus]; exists {
		return unifiedStatus, true
	}

	// 未找到映射，返回原状态
	return rawStatus, false
}
