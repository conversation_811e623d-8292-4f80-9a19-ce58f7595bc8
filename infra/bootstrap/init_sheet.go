package bootstrap

import (
	"brand-bidding-service/infra/constant"
	"brand-bidding-service/infra/external_gateway/sheetlib"
	"context"
	"fmt"
	"google.golang.org/api/sheets/v4"
)

var SheetService *sheets.Service

func InitSheet() error {
	ctx := context.Background()
	var err error
	SheetService, err = sheetlib.InitService(ctx, constant.GoogleSheetAuth)
	if err != nil {
		fmt.Println("sheetlib.InitService failed: ", err)
		return err
	}
	return nil
}
