package bootstrap

import (
	"brand-bidding-service/infra/dao"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	_ "github.com/mattn/go-sqlite3"
	"go.uber.org/zap"
)

var (
	DB          *sql.DB
	OrderDAO    *dao.OrderDAO
	MerchantDAO *dao.MerchantDAO
	once        sync.Once
)

// InitDB 初始化数据库连接
func InitDB() error {
	// 获取项目根目录路径
	rootDir, err := getProjectRoot()
	if err != nil {
		return fmt.Errorf("获取项目根目录失败: %w", err)
	}

	// SQLite 数据库文件路径 - 存放在专门的data目录下
	dbPath := filepath.Join(rootDir, "data", "app.db")
	// 确保数据库文件的目录存在
	if err := ensureDBDirectory(dbPath); err != nil {
		fmt.Printf("❌ 创建数据库目录失败: %v\n", err)
		zap.L().Error("创建数据库目录失败", zap.Error(err))
		return err
	}

	// 连接SQLite数据库 - 使用优化的连接字符串
	connectionString := dbPath + "?_foreign_keys=on&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=10000&_temp_store=memory"
	db, err := sql.Open("sqlite3", connectionString)
	if err != nil {
		fmt.Printf("❌ 连接SQLite数据库失败: %v\n", err)
		zap.L().Error("Failed to connect to SQLite database", zap.Error(err))
		return err
	}

	// 测试数据库连接
	err = db.Ping()
	if err != nil {
		fmt.Printf("❌ 数据库连接测试失败: %v\n", err)
		zap.L().Error("Failed to ping SQLite database", zap.Error(err))
		db.Close()
		return err
	}
	// 设置连接池参数 - 优化配置
	db.SetMaxOpenConns(25)   // 增加最大连接数
	db.SetMaxIdleConns(10)   // 增加空闲连接数
	db.SetConnMaxLifetime(0) // 连接不过期
	// 执行 SQLite 优化设置
	optimizations := []struct {
		name string
		sql  string
	}{
		{"启用 WAL 模式", "PRAGMA journal_mode=WAL;"},
		{"设置同步模式", "PRAGMA synchronous=NORMAL;"},
		{"设置缓存大小", "PRAGMA cache_size=10000;"},
		{"启用外键约束", "PRAGMA foreign_keys=ON;"},
		{"设置临时存储", "PRAGMA temp_store=memory;"},
	}

	for _, opt := range optimizations {
		if _, err := db.Exec(opt.sql); err != nil {
			fmt.Printf("⚠️  %s失败: %v\n", opt.name, err)
			// 优化设置失败不应该中断初始化
		}
	}

	// 自动创建和迁移订单表
	if err := autoMigrate(db); err != nil {
		fmt.Printf("❌ 自动表结构迁移失败: %v\n", err)
		zap.L().Error("自动表结构迁移失败", zap.Error(err))
		db.Close()
		return err
	}

	// 验证表结构
	if err := validateTableStructure(db); err != nil {
		fmt.Printf("⚠️  表结构验证警告: %v\n", err)
		// 表结构验证失败不中断初始化，仅记录警告
		zap.L().Warn("表结构验证警告", zap.Error(err))
	}

	DB = db
	// 初始化DAO实例
	OrderDAO = dao.NewOrderDAO(db)
	MerchantDAO = dao.NewMerchantDAO(db)
	fmt.Println("🎉 数据库初始化完成")
	return nil
}

// getProjectRoot 获取项目根目录路径
func getProjectRoot() (string, error) {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 从当前目录向上查找，直到找到包含 go.mod 文件的目录
	dir := wd
	for {
		// 检查当前目录是否包含 go.mod 文件
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return dir, nil
		}

		// 向上一级目录
		parent := filepath.Dir(dir)
		if parent == dir {
			// 已经到达根目录，仍未找到
			break
		}
		dir = parent
	}

	return "", fmt.Errorf("未找到项目根目录（包含go.mod的目录）")
}

// ensureDBDirectory 确保数据库文件的目录存在
func ensureDBDirectory(dbPath string) error {
	dir := filepath.Dir(dbPath)
	if dir == "." {
		return nil // 当前目录，无需创建
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	} else if err != nil {
		return fmt.Errorf("检查目录失败: %w", err)
	}

	return nil
}

// autoMigrate 自动执行表结构迁移（类似GORM的AutoMigrate）
func autoMigrate(db *sql.DB) error {
	// 初始化订单表
	orderDAO := dao.NewOrderDAO(db)
	if err := orderDAO.CreateTable(); err != nil {
		fmt.Printf("❌ 订单表迁移失败: %v\n", err)
		return fmt.Errorf("订单表迁移失败: %w", err)
	}

	// 初始化商家表
	merchantDAO := dao.NewMerchantDAO(db)
	if err := merchantDAO.CreateTable(); err != nil {
		fmt.Printf("❌ 商家表迁移失败: %v\n", err)
		return fmt.Errorf("商家表迁移失败: %w", err)
	}

	return nil
}

// validateTableStructure 验证数据库表结构
func validateTableStructure(db *sql.DB) error {
	// 检查订单表是否存在
	var tableName string
	err := db.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'").Scan(&tableName)
	if err != nil {
		return fmt.Errorf("订单表不存在: %w", err)
	}

	// 检查必要字段是否存在
	requiredFields := []string{"id", "user_name", "account", "merchant_name", "order_status"}
	rows, err := db.Query("PRAGMA table_info(orders)")
	if err != nil {
		return fmt.Errorf("无法获取表结构: %w", err)
	}
	defer rows.Close()

	existingFields := make(map[string]bool)
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull int
		var defaultValue interface{}
		var pk int

		if err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
			return fmt.Errorf("扫描表字段失败: %w", err)
		}
		existingFields[name] = true
	}

	// 检查必要字段
	var missingFields []string
	for _, field := range requiredFields {
		if !existingFields[field] {
			missingFields = append(missingFields, field)
		}
	}

	if len(missingFields) > 0 {
		return fmt.Errorf("缺少必要字段: %v", missingFields)
	}
	return nil
}

// GetDB 获取数据库连接
func GetDB() *sql.DB {
	if DB == nil {
		zap.L().Warn("数据库连接为空，可能初始化失败")
	}
	return DB
}

// GetOrderDAO 获取订单DAO实例（单例模式）
func GetOrderDAO() *dao.OrderDAO {
	if OrderDAO == nil {
		zap.L().Warn("订单DAO为空，可能数据库初始化失败")
	}
	return OrderDAO
}

// GetMerchantDAO 获取商家DAO实例（单例模式）
func GetMerchantDAO() *dao.MerchantDAO {
	if MerchantDAO == nil {
		zap.L().Warn("商家DAO为空，可能数据库初始化失败")
	}
	return MerchantDAO
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if DB != nil {
		fmt.Println("关闭数据库连接...")
		err := DB.Close()
		DB = nil
		OrderDAO = nil
		MerchantDAO = nil
		return err
	}
	return nil
}
