package bootstrap

import (
	"go.uber.org/zap"
)

func Init() (err error) {
	// 初始化日志
	if err := InitLogger("release"); err != nil {
		zap.L().Error("初始化 日志 失败", zap.Error(err))
		return err
	}

	// 初始化谷歌链接
	if err := InitSheet(); err != nil {
		zap.L().Error("初始化 谷歌认证 失败", zap.Error(err))
		return err
	}

	// 初始化数据库
	if err := InitDB(); err != nil {
		zap.L().Error("初始化 数据库 失败", zap.Error(err))
		return err
	}

	return nil
}
