package main

import (
	"context"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type GetMerchantsResp struct {
	MerchantSlug string
	MerchantName string
	Commission   string
	Categories   string
	Country      string
}

const (
	host = "https://www.rewardany.com"

	apiAdminGetMerchants = "/stores/all/p/"
)

const (
	requestInterval = time.Second * 4
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时3s
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := baseUrl
	if strings.Contains(baseUrl, "?") {
		fullUrl = fmt.Sprintf("%s&%s", baseUrl, urlValues.Encode())
	} else {
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")
	req.Header.Set("accept", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

func main() {
	ctx := context.Background()
	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, host+apiAdminGetMerchants+"3", nil)
	if err != nil {
		fmt.Println("rewardanylib GetMerchants create request failed", err.Error())
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("rewardanylib GetMerchants request failed", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("rewardanylib GetMerchants read body failed", err.Error())
	}

	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(body)))
	if err != nil {
		fmt.Println("rewardanylib GetMerchants parse HTML failed", err.Error())
	}

	// 存储商家信息
	var merchants []GetMerchantsResp

	// 解析商家信息
	doc.Find("div.card-flex-1 div.col-lg-3.col-md-4.col-sm-6.col-12").Each(func(i int, s *goquery.Selection) {
		merchantSlug := ""
		merchantName := ""
		if href, exists := s.Find("a.card.card-store").Attr("href"); exists {
			merchantSlug = strings.TrimSpace(strings.ReplaceAll(strings.ReplaceAll(href, "/store/", ""), "\n", ""))
		}
		merchantName = merchantSlug

		// 获取佣金比例
		commission := strings.TrimSpace(s.Find("div.sc-rate i").Text())

		// 获取国家信息
		region := strings.TrimSpace(s.Find("div.store-region").Text())

		// 只添加有效的商家信息
		if merchantSlug != "" && commission != "" && region != "" {
			merchant := GetMerchantsResp{
				MerchantSlug: merchantSlug,
				MerchantName: merchantName,
				Commission:   commission,
				Categories:   "test",
				Country:      region,
			}
			merchants = append(merchants, merchant)
		}
	})

	// 打印获取到的商家信息
	for _, merchant := range merchants {
		fmt.Printf("商家: %s, 佣金: %s, 国家: %s\n", merchant.MerchantName, merchant.Commission, merchant.Country)
	}
	fmt.Println(len(merchants))
}
