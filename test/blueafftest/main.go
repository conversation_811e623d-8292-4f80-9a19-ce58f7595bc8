package main

import (
	"brand-bidding-service/infra/ecode"
	"brand-bidding-service/test/blueafftest/blueaffvo"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

const (
	host = "https://api.blueaff.com" // BlueAff API 的 Base URL

	apiGetOffers = "/publisher/offer/list"
	maxPerPage   = 1000 // BlueAff API 允许的最大每页记录数
)

const (
	hostByPortal                    = "https://api.blueaff.com"
	apiGetOrderTransactionsByPortal = "/api/publisher/report/conversions"

	apiGetMerchantsByPortal = "/publisher/offer/list"
	apiGetConversions       = "/publisher/conversion/list"
)

const (
	requestInterval = time.Second
	overtimeTime    = time.Second * 180
)

func remoteInvokeWithUrl(ctx context.Context, baseUrl string, method string, params map[string]interface{}, headers map[string]string, body io.Reader) (respBody []byte, err error) {
	// space 请求，延时3s
	time.Sleep(requestInterval)

	client := &http.Client{
		Timeout: overtimeTime,
	}

	urlValues := url.Values{}
	for key, value := range params {
		urlValues.Add(key, value.(string))
	}
	fullUrl := baseUrl
	if strings.Contains(baseUrl, "?") {
		fullUrl = fmt.Sprintf("%s&%s", baseUrl, urlValues.Encode())
	} else {
		fullUrl = fmt.Sprintf("%s?%s", baseUrl, urlValues.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, method, fullUrl, body)
	if err != nil {
		return nil, err
	}
	// 默认 header
	req.Header.Set("content-type", "application/json")

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return respBody, err
	}
	defer resp.Body.Close()
	respBody, err = ioutil.ReadAll(resp.Body)
	fmt.Println(string(respBody))
	if err != nil {
		return respBody, err
	}
	return respBody, nil
}

func GetOrderTransactionsByPortal(apiKey string, status string, page int, limit int, startDay int, endDay int) (*blueaffvo.GetOrderTransactionsResp, *ecode.ErrCode) {
	ctx := context.Background()
	currentDate := time.Now()
	// 计算开始和结束日期
	beginDate := currentDate.AddDate(0, 0, startDay).Format("2006-01-02")
	endDate := currentDate.AddDate(0, 0, endDay).Format("2006-01-02")

	params := map[string]interface{}{}
	headers := map[string]string{
		"API-KEY":      apiKey,
		"Content-Type": "application/json",
	}
	requestBody := map[string]interface{}{
		"start_date":   beginDate,
		"end_date":     endDate,
		"current_page": page,
		"page_size":    limit,
	}
	// 将请求体编码为 JSON
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	resp, err := remoteInvokeWithUrl(ctx, hostByPortal+apiGetConversions, http.MethodPost, params, headers, bytes.NewBuffer(jsonBody))

	if err != nil {
		return nil, ecode.With(ecode.ErrCodeHTTP, err)
	}
	getOrderTransactionsResp := new(blueaffvo.GetOrderTransactionsResp)
	err = json.Unmarshal(resp, getOrderTransactionsResp)
	if err != nil {
		zap.L().Error("blueafflib GetOrderTransactionsByPortal json.Unmarshal failed", zap.Error(err))
		return nil, ecode.With(ecode.ErrCodeJson, err)
	}
	return getOrderTransactionsResp, nil
}

func BatchGetOrderTransactions(accountName string, token string, tokenEf string, status string, page int, limit int, startDay int, endDay int, exchangeRatesUsdMap map[string]float64) ([]map[string]interface{}, *ecode.ErrCode) {
	allOrderList := make([]map[string]interface{}, 0)
	for {
		getOrderTransactionsByPortalResp, err := GetOrderTransactionsByPortal(token, status, page, limit, startDay, endDay)
		if err != nil {
			fmt.Println("blueafflib GetOrderTransactionsByPortal GetOrderTransactions failed", err.String())
			continue
		}
		// 将获取到的订单添加到总列表中
		allOrderList = append(allOrderList, convertOrdersToSliceByPortal(accountName, getOrderTransactionsByPortalResp)...)
		fmt.Println(allOrderList)
		// 如果获取到的订单长度为0，表示没有更多订单
		if page >= getOrderTransactionsByPortalResp.Data.Paging.TotalPage {
			break
		}

		// 更新 offset，以便获取下一批订单
		page += 1
	}

	// 把 allAffiliateOrderList 加入到 allOrderList 中
	allOrderMap := make(map[interface{}]bool, 0)
	for i := range allOrderList {
		allOrderMap[allOrderList[i]["id"]] = true
	}
	return allOrderList, nil
}

func convertOrdersToSliceByPortal(accountName string, orders *blueaffvo.GetOrderTransactionsResp) []map[string]interface{} {
	// publish 全部转换为了 美元，无需进行转换
	result := make([]map[string]interface{}, 0, len(orders.Data.Records))
	now := time.Now()
	nowTimeStr := now.Format("2006-01-02 15:04:05")
	// 遍历每个 Order，将其字段转换为 map
	for _, order := range orders.Data.Records {
		tagCode := order.Sub1
		if len(tagCode) <= 0 {
			tagCode = "-"
		}
		// 处理时间
		t, err := time.Parse("2006-01-02 15:04:05", order.ConvTime)
		if err != nil {
			fmt.Println("Error parsing time:", err)
		}
		formattedDateTime := t.Format("2006-1-2 15:04:05")
		formattedDateTimeDay := t.Format("2006-1-2")
		orderTimeHour := t.Hour()
		// Convert string to float64
		estRevenue, err := strconv.ParseFloat(order.EstRevenue, 64)
		if err != nil {
			fmt.Println("Error converting string to float:", err)
			continue
		}

		orderMap := map[string]interface{}{
			"id":               order.ConversionId,
			"account":          accountName,
			"order_id":         order.OrderId,
			"order_time_sec":   formattedDateTime,
			"order_time_day":   formattedDateTimeDay,
			"merchant_name":    order.OfferName,
			"commission":       estRevenue,
			"order_status":     order.Status,
			"hour_text":        orderTimeHour,
			"hour_int":         orderTimeHour,
			"tag1":             tagCode,
			"tag2":             order.Sub2,
			"ip":               order.ConversionIp,
			"referer_url":      order.ClickId,
			"customer_country": order.Country,
			"currency":         order.Currency,
			"commission_usd":   estRevenue,
			"create_time":      nowTimeStr,
			"update_time":      nowTimeStr,
		}

		result = append(result, orderMap)
	}

	return result
}

func main() {

	accountName := "MyBlueAffAccount" // 示例账户名

	// 2. 获取所有 Offer 列表
	fmt.Println("\nFetching all offers...")
	// 可以传入 filters，例如 {"filters[status]": "active"}
	offers, errCode := BatchGetOrderTransactions(accountName, "309b642d4acda9749b07aab3bfd8b1fb7c444235", "", "", 1, 1000, -10, 1, nil)
	if errCode != nil {
		zap.L().Error("Error fetching offers", zap.Error(errCode))
		os.Exit(1)
	}
	fmt.Printf("Successfully fetched %d offers.\n", len(offers))
	if len(offers) > 0 {
		fmt.Printf("Example Offer (first one): %+v\n", offers[0])
	}

	// 模拟一些处理时间
	time.Sleep(1 * time.Second)

	//// 3. 获取所有转化列表
	//fmt.Println("\nFetching all conversions...")
	//// 可以传入 filters，例如 {"filters[status]": "approved", "filters[created_at_from]": "2023-01-01"}
	//conversions, errCode := BatchGetConversionList(accountName, apiKey, nil)
	//if errCode != nil {
	//	zap.L().Error("Error fetching conversions", zap.Error(errCode))
	//	os.Exit(1)
	//}
	//fmt.Printf("Successfully fetched %d conversions.\n", len(conversions))
	//if len(conversions) > 0 {
	//	fmt.Printf("Example Conversion (first one): %+v\n", conversions[0])
	//}
}
