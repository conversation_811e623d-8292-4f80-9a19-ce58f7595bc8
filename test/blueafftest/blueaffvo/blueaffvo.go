package blueaffvo

type GetMerchantsResp struct {
	Code string `json:"code"`
	Info string `json:"info"`
	Data struct {
		Paging struct {
			CurrentPage int `json:"current_page"`
			PageSize    int `json:"page_size"`
			Count       int `json:"count"`
			TotalPage   int `json:"total_page"`
		} `json:"paging"`
		Records []struct {
			Id                 int      `json:"id"`
			OfferId            string   `json:"offer_id"`
			OfferName          string   `json:"offer_name"`
			Status             string   `json:"status"`
			Domain             string   `json:"domain"`
			Category           string   `json:"category"`
			Currency           string   `json:"currency"`
			Description        string   `json:"description"`
			PrimaryRegion      string   `json:"primary_region"`
			SupportedRegion    []string `json:"supported_region"`
			Channel            []string `json:"channel"`
			CommissionType     string   `json:"commission_type"`
			CommissionValueMin string   `json:"commission_value_min"`
			IsDeeplink         string   `json:"is_deeplink"`
			CommissionValueMax string   `json:"commission_value_max"`
			TrackingLink       string   `json:"tracking_link"`
			CreateTime         string   `json:"create_time"`
			UpdateTime         string   `json:"update_time"`
		} `json:"records"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}

type GetOrderTransactionsResp struct {
	Code string `json:"code"`
	Info string `json:"info"`
	Data struct {
		Paging struct {
			CurrentPage int `json:"current_page"`
			PageSize    int `json:"page_size"`
			Count       int `json:"count"`
			TotalPage   int `json:"total_page"`
		} `json:"paging"`
		Records []struct {
			Id           int    `json:"id"`
			ConvTime     string `json:"conv_time"`
			OfferId      string `json:"offer_id"`
			OfferName    string `json:"offer_name"`
			SaleAmount   string `json:"sale_amount"`
			EstRevenue   string `json:"est_revenue"`
			Currency     string `json:"currency"`
			PartnerId    string `json:"partner_id"`
			SessionIp    string `json:"session_ip"`
			ClickId      string `json:"click_id"`
			ConversionId string `json:"conversion_id"`
			OrderId      string `json:"order_id"`
			ConversionIp string `json:"conversion_ip"`
			Country      string `json:"country"`
			CouponCode   string `json:"coupon_code"`
			Status       string `json:"status"`
			Sub1         string `json:"sub1"`
			Sub2         string `json:"sub2"`
			Sub3         string `json:"sub3"`
			Sub4         string `json:"sub4"`
			Sub5         string `json:"sub5"`
			SourceId     string `json:"source_id"`
			RejectedTime string `json:"rejected_time"`
			ApprovedTime string `json:"approved_time"`
			PaidTime     string `json:"paid_time"`
		} `json:"records"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}
