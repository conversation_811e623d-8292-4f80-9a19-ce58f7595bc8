package service

import (
	"brand-bidding-service/domain/order/entity"
	"brand-bidding-service/infra/dao"
)

// OrderService 订单服务接口
type OrderService struct {
	orderDAO *dao.OrderDAO
}

// NewOrderService 创建订单服务
func NewOrderService(orderDAO *dao.OrderDAO) *OrderService {
	return &OrderService{
		orderDAO: orderDAO,
	}
}

// GetOrders 获取订单列表
func (s *OrderService) GetOrders(conditions map[string]interface{}) ([]entity.Order, error) {
	return s.orderDAO.GetOrders(conditions)
}

// GetTotalCommission 获取订单总佣金
func (s *OrderService) GetTotalCommission(conditions map[string]interface{}) (float64, error) {
	return s.orderDAO.GetTotalCommission(conditions)
}