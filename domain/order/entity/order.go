package entity

// OrderConstraints 订单表约束定义
type OrderConstraints struct {
	// 复合唯一约束：去重核心字段
	UniqueFields []string
	// 索引字段
	IndexFields []string
}

// GetOrderConstraints 获取订单约束配置
func GetOrderConstraints() OrderConstraints {
	return OrderConstraints{
		UniqueFields: []string{"conversion_id", "user_name", "platform_type", "account", "merchant_id"},
		IndexFields:  []string{"user_name", "account", "order_time_day", "merchant_name", "order_status", "conversion_id", "platform_type", "merchant_id", "created_at"},
	}
}

// Order 订单实体
type Order struct {
	ID              string `json:"id" db:"id"`             // 自增唯一主键ID
	UserName        string `json:"user" db:"user_name"`    // user name
	Account         string `json:"account" db:"account"`   // 订单账号
	OrderID         string `json:"order_id" db:"order_id"` // 订单号
	OrderTimeSec    string `json:"order_time_sec" db:"order_time_sec"`
	OrderTimeDay    string `json:"order_time_day" db:"order_time_day"` // 订单日期
	MerchantName    string `json:"merchant_name" db:"merchant_name"`
	OrderStatus     string `json:"order_status" db:"order_status"`
	Tag1            string `json:"tag_1" db:"tag_1"`
	Tag2            string `json:"tag_2" db:"tag_2"`
	Ip              string `json:"ip" db:"ip"`
	RefererUrl      string `json:"referer_url" db:"referer_url"`
	CustomerCountry string `json:"customer_country" db:"customer_country"`

	// 新增去重关键字段
	ConversionId string `json:"conversion_id" db:"conversion_id"` // 上级平台的订单ID
	PlatformType string `json:"platform_type" db:"platform_type"` // 平台类型（来自OrderAccountList的type）
	MerchantId   string `json:"merchant_id" db:"merchant_id"`     // 商家ID（来自上级平台）

	OrderAmount    float64 `json:"order_amount" db:"order_amount"`         // 订单金额（不同货币）
	OrderAmountUsd float64 `json:"order_amount_usd" db:"order_amount_usd"` // 订单金额（美金）

	Commission float64 `json:"commission" db:"commission"` // 订单佣金，全部转换为美金，用这个佣金值

	Currency      string  `json:"currency" db:"currency"`
	CommissionUsd float64 `json:"commission_usd" db:"commission_usd"` // 订单佣金，全部转换为美金，用这个佣金值
	CreatedAt     string  `json:"created_at" db:"created_at"`         // 记录创建时间
	UpdatedAt     string  `json:"updated_at" db:"updated_at"`         // 记录更新时间
}
