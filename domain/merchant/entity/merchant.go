package entity

import (
	"encoding/json"
	"time"
)

// AccountInfo 账户特定信息结构体
type AccountInfo struct {
	TrackURL string `json:"track_url"`
	Status   string `json:"status"`
	UserName string `json:"user_name"`
	Account  string `json:"account"`
}

type Merchant struct {
	ID                 string `db:"id" json:"id"`
	MerchantID         string `json:"merchant_id" db:"merchant_id"`                 // 平台商家ID
	MerchantSlug       string `json:"merchant_slug" db:"merchant_slug"`             // 商家slug
	MerchantName       string `json:"merchant_name" db:"merchant_name"`             // 商家名称
	CategoryName       string `json:"category_name" db:"category_name"`             // 分类名称
	Country            string `json:"country,omitempty" db:"country"`               // 国家信息
	SupportedCountries string `json:"supported_countries" db:"supported_countries"` // 支持返利的国家
	Website            string `json:"website" db:"website"`                         // 商家网站
	OriginalDomain     string `json:"original_domain" db:"original_domain"`         // 商家原域名
	CashbackInfo       string `json:"cashback_rate" db:"cashback_rate"`             // 返现比例
	Sub1               string `json:"sub1" db:"sub1"`                               // 参数映射
	PlatformType       string `json:"platform_type" db:"platform_type"`             // 平台类型

	// 账户特定信息，存储为JSON格式以支持多账户
	AccountsInfo string `json:"accounts_info" db:"accounts_info"` // 多账户信息JSON数组

	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// GetAccountsInfo 获取账户信息列表
func (m *Merchant) GetAccountsInfo() ([]AccountInfo, error) {
	var accountsInfo []AccountInfo
	if m.AccountsInfo == "" || m.AccountsInfo == "[]" {
		return accountsInfo, nil
	}
	err := json.Unmarshal([]byte(m.AccountsInfo), &accountsInfo)
	return accountsInfo, err
}

// SetAccountsInfo 设置账户信息列表
func (m *Merchant) SetAccountsInfo(accountsInfo []AccountInfo) error {
	data, err := json.Marshal(accountsInfo)
	if err != nil {
		return err
	}
	m.AccountsInfo = string(data)
	return nil
}

// AddOrUpdateAccountInfo 添加或更新特定账户的信息
func (m *Merchant) AddOrUpdateAccountInfo(newAccountInfo AccountInfo) error {
	accountsInfo, err := m.GetAccountsInfo()
	if err != nil {
		return err
	}

	// 查找是否存在相同的账户
	found := false
	for i, info := range accountsInfo {
		if info.UserName == newAccountInfo.UserName && info.Account == newAccountInfo.Account {
			// 更新现有账户信息
			accountsInfo[i] = newAccountInfo
			found = true
			break
		}
	}

	// 如果不存在，添加新的账户信息
	if !found {
		accountsInfo = append(accountsInfo, newAccountInfo)
	}

	return m.SetAccountsInfo(accountsInfo)
}

// GetMerchantConstraints 获取商家约束配置
func GetMerchantConstraints() struct {
	UniqueFields []string
	IndexFields  []string
} {
	return struct {
		UniqueFields []string
		IndexFields  []string
	}{
		// 去重键：MerchantID + MerchantSlug + PlatformType
		UniqueFields: []string{"merchant_id", "merchant_slug", "platform_type"},
		// 索引字段
		IndexFields: []string{"merchant_id", "merchant_slug", "platform_type", "created_at", "updated_at"},
	}
}
